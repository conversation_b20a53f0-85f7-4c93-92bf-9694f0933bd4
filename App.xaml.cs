using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using LMSCBTSystem.Configuration;

using LMSCBTSystem.Services;
using LMSCBTSystem.Utils;
using LMSCBTSystem.Views;
using LMSCBTSystem.ViewModels;
using Serilog;

namespace LMSCBTSystem
{
    /// <summary>
    /// Main application class
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;
        private IServiceProvider? _serviceProvider;

        protected override async void OnStartup(StartupEventArgs e)
        {
            try
            {
                // Initialize configuration
                var configuration = BuildConfiguration();

                // Initialize logging
                LoggingConfiguration.InitializeLoggingDirectory(configuration);

                // Build host
                _host = CreateHostBuilder(e.Args, configuration).Build();
                _serviceProvider = _host.Services;

                // Start host
                await _host.StartAsync();

                // Initialize application
                await InitializeApplicationAsync();

                // Show main window
                ShowMainWindow();

                base.OnStartup(e);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Application startup failed: {ex.Message}", "Startup Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown(1);
            }
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            try
            {
                if (_host != null)
                {
                    await _host.StopAsync();
                    _host.Dispose();
                }

                Log.CloseAndFlush();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Application shutdown error: {ex.Message}", "Shutdown Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }

            base.OnExit(e);
        }

        private static IConfiguration BuildConfiguration()
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT") ?? "Production"}.json", 
                    optional: true, reloadOnChange: true)
                .AddEnvironmentVariables();

            return builder.Build();
        }

        private static IHostBuilder CreateHostBuilder(string[] args, IConfiguration configuration)
        {
            return Host.CreateDefaultBuilder(args)
                .ConfigureLogging(configuration)
                .ConfigureServices((context, services) =>
                {
                    // Add configuration
                    services.AddAppConfiguration(configuration);

                    // Add database
                    services.AddDatabaseServices(configuration);

                    // Add application services
                    services.AddApplicationServices();

                    // Add ViewModels
                    services.AddViewModels();

                    // Add utilities
                    services.AddUtilities();

                    // Add custom logging
                    services.AddCustomLogging(configuration);
                });
        }

        private async Task InitializeApplicationAsync()
        {
            try
            {
                var logger = _serviceProvider?.GetService<ILogger<App>>();
                logger?.LogInformation("Initializing LMS CBT System...");

                // Validate configuration
                var config = _serviceProvider?.GetService<AppConfiguration>();
                config?.ValidateConfiguration();

                // Initialize database
                var dbService = _serviceProvider?.GetService<IDatabaseService>();
                if (dbService != null)
                {
                    await dbService.InitializeAsync();
                }

                // Initialize default data
                var dataInitService = _serviceProvider?.GetService<IDataInitializationService>();
                if (dataInitService != null)
                {
                    var isInitialized = await dataInitService.IsSystemInitializedAsync();
                    if (!isInitialized)
                    {
                        await dataInitService.InitializeDefaultDataAsync();
                    }
                }

                logger?.LogInformation("LMS CBT System initialized successfully");
            }
            catch (Exception ex)
            {
                var logger = _serviceProvider?.GetService<ILogger<App>>();
                logger?.LogError(ex, "Failed to initialize application");
                throw;
            }
        }

        private void ShowMainWindow()
        {
            try
            {
                // Create and show login window
                var loginWindow = _serviceProvider?.GetService<LoginWindow>();
                if (loginWindow != null)
                {
                    MainWindow = loginWindow;
                    loginWindow.Show();
                }
                else
                {
                    // Fallback: create login window manually
                    var loginViewModel = _serviceProvider?.GetService<LoginViewModel>();
                    MainWindow = new LoginWindow { DataContext = loginViewModel };
                    MainWindow.Show();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to show main window: {ex.Message}", "Window Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown(1);
            }
        }

        /// <summary>
        /// Get service from DI container
        /// </summary>
        public static T? GetService<T>() where T : class
        {
            return ((App)Current)?._serviceProvider?.GetService<T>();
        }

        /// <summary>
        /// Get required service from DI container
        /// </summary>
        public static T GetRequiredService<T>() where T : class
        {
            return ((App)Current)?._serviceProvider?.GetRequiredService<T>() 
                ?? throw new InvalidOperationException($"Service {typeof(T).Name} not found");
        }

        /// <summary>
        /// Show error message
        /// </summary>
        public static void ShowError(string message, string title = "Error")
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// Show warning message
        /// </summary>
        public static void ShowWarning(string message, string title = "Warning")
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        /// <summary>
        /// Show information message
        /// </summary>
        public static void ShowInfo(string message, string title = "Information")
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// Show confirmation dialog
        /// </summary>
        public static bool ShowConfirmation(string message, string title = "Confirmation")
        {
            return MessageBox.Show(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes;
        }
    }

    /// <summary>
    /// Service registration extensions
    /// </summary>
    public static class ServiceRegistrationExtensions
    {
        /// <summary>
        /// Add application services
        /// </summary>
        public static IServiceCollection AddApplicationServices(this IServiceCollection services)
        {
            // Register services
            services.AddScoped<IAuthService, AuthService>();
            services.AddScoped<IDatabaseService, DatabaseService>();
            services.AddScoped<ISyncService, SyncService>();
            services.AddScoped<IDataInitializationService, DataInitializationService>();

            return services;
        }

        /// <summary>
        /// Add ViewModels
        /// </summary>
        public static IServiceCollection AddViewModels(this IServiceCollection services)
        {
            // Register ViewModels
            services.AddTransient<LoginViewModel>();
            services.AddTransient<MainViewModel>();
            services.AddTransient<DashboardViewModel>();
            services.AddTransient<UserManagementViewModel>();
            services.AddTransient<CourseManagementViewModel>();
            services.AddTransient<TestManagementViewModel>();

            return services;
        }

        /// <summary>
        /// Add utilities
        /// </summary>
        public static IServiceCollection AddUtilities(this IServiceCollection services)
        {
            // Register utilities
            services.AddSingleton<JwtUtil>();

            return services;
        }

        /// <summary>
        /// Add Views
        /// </summary>
        public static IServiceCollection AddViews(this IServiceCollection services)
        {
            // Register Views
            services.AddTransient<LoginWindow>();
            services.AddTransient<MainWindow>();

            return services;
        }
    }
}
