using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;


namespace LMSCBTSystem.Configuration
{
    /// <summary>
    /// Database configuration and setup
    /// </summary>
    public static class DatabaseConfiguration
    {
        /// <summary>
        /// Configure database services
        /// </summary>
        public static IServiceCollection AddDatabaseServices(this IServiceCollection services, IConfiguration config)
        {
            var connectionString = config.GetConnectionString("DefaultConnection") 
                ?? config["Database:Offline:ConnectionString"];

            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("Database connection string not found in configuration");
            }

            // Ensure data directory exists
            EnsureDataDirectoryExists(connectionString);

            // Add Entity Framework
            services.AddDbContext<LMSDbContext>(options =>
            {
                options.UseSqlite(connectionString);
                
                // Enable sensitive data logging in development
                #if DEBUG
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
                #endif
                
                // Configure logging
                options.LogTo(Console.WriteLine, LogLevel.Information);
            });

            // Add database health check
            services.AddHealthChecks()
                .AddDbContextCheck<LMSDbContext>("database");

            return services;
        }

        /// <summary>
        /// Initialize database with migrations and seed data
        /// </summary>
        public static async Task InitializeDatabaseAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<LMSDbContext>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<LMSDbContext>>();

            try
            {
                logger.LogInformation("Initializing database...");

                // Ensure database is created
                await context.Database.EnsureCreatedAsync();

                // Apply any pending migrations
                if (context.Database.GetPendingMigrations().Any())
                {
                    logger.LogInformation("Applying database migrations...");
                    await context.Database.MigrateAsync();
                }

                // Seed additional data if needed
                await SeedAdditionalDataAsync(context, logger);

                logger.LogInformation("Database initialization completed successfully");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while initializing the database");
                throw;
            }
        }

        /// <summary>
        /// Test database connection
        /// </summary>
        public static async Task<bool> TestConnectionAsync(IServiceProvider serviceProvider)
        {
            try
            {
                using var scope = serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<LMSDbContext>();
                
                return await context.Database.CanConnectAsync();
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Get database statistics
        /// </summary>
        public static async Task<DatabaseStatistics> GetDatabaseStatisticsAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<LMSDbContext>();

            var stats = new DatabaseStatistics
            {
                UserCount = await context.Users.CountAsync(),
                CourseCount = await context.Courses.CountAsync(),
                TestCount = await context.Tests.CountAsync(),
                QuestionCount = await context.Questions.CountAsync(),
                TestResultCount = await context.TestResults.CountAsync(),
                AssignmentCount = await context.Assignments.CountAsync(),
                AttendanceCount = await context.Attendances.CountAsync(),
                IsOnlineMode = false // Always offline for SQLite
            };

            return stats;
        }

        /// <summary>
        /// Backup database
        /// </summary>
        public static async Task<bool> BackupDatabaseAsync(IServiceProvider serviceProvider, string backupPath)
        {
            try
            {
                using var scope = serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<LMSDbContext>();
                var logger = scope.ServiceProvider.GetRequiredService<ILogger<LMSDbContext>>();

                var connectionString = context.Database.GetConnectionString();
                if (string.IsNullOrEmpty(connectionString))
                    return false;

                // Extract database file path from connection string
                var dbPath = ExtractDatabasePath(connectionString);
                if (string.IsNullOrEmpty(dbPath) || !File.Exists(dbPath))
                    return false;

                // Ensure backup directory exists
                var backupDir = Path.GetDirectoryName(backupPath);
                if (!string.IsNullOrEmpty(backupDir) && !Directory.Exists(backupDir))
                {
                    Directory.CreateDirectory(backupDir);
                }

                // Copy database file
                File.Copy(dbPath, backupPath, true);

                logger.LogInformation("Database backed up to: {BackupPath}", backupPath);
                return true;
            }
            catch (Exception ex)
            {
                var logger = serviceProvider.GetService<ILogger<LMSDbContext>>();
                logger?.LogError(ex, "Failed to backup database to: {BackupPath}", backupPath);
                return false;
            }
        }

        /// <summary>
        /// Restore database from backup
        /// </summary>
        public static async Task<bool> RestoreDatabaseAsync(IServiceProvider serviceProvider, string backupPath)
        {
            try
            {
                if (!File.Exists(backupPath))
                    return false;

                using var scope = serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<LMSDbContext>();
                var logger = scope.ServiceProvider.GetRequiredService<ILogger<LMSDbContext>>();

                var connectionString = context.Database.GetConnectionString();
                if (string.IsNullOrEmpty(connectionString))
                    return false;

                var dbPath = ExtractDatabasePath(connectionString);
                if (string.IsNullOrEmpty(dbPath))
                    return false;

                // Close all connections
                await context.Database.CloseConnectionAsync();

                // Copy backup file to database location
                File.Copy(backupPath, dbPath, true);

                logger.LogInformation("Database restored from: {BackupPath}", backupPath);
                return true;
            }
            catch (Exception ex)
            {
                var logger = serviceProvider.GetService<ILogger<LMSDbContext>>();
                logger?.LogError(ex, "Failed to restore database from: {BackupPath}", backupPath);
                return false;
            }
        }

        private static void EnsureDataDirectoryExists(string connectionString)
        {
            var dbPath = ExtractDatabasePath(connectionString);
            if (!string.IsNullOrEmpty(dbPath))
            {
                var directory = Path.GetDirectoryName(dbPath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
            }
        }

        private static string? ExtractDatabasePath(string connectionString)
        {
            // Extract database file path from SQLite connection string
            var parts = connectionString.Split(';');
            foreach (var part in parts)
            {
                var keyValue = part.Split('=');
                if (keyValue.Length == 2 && 
                    keyValue[0].Trim().Equals("Data Source", StringComparison.OrdinalIgnoreCase))
                {
                    return keyValue[1].Trim();
                }
            }
            return null;
        }

        private static async Task SeedAdditionalDataAsync(LMSDbContext context, ILogger logger)
        {
            // Check if admin user exists
            var adminUser = await context.Users.FirstOrDefaultAsync(u => u.Username == "admin");
            if (adminUser == null)
            {
                logger.LogInformation("Creating default admin user...");

                var adminRole = await context.Roles.FirstOrDefaultAsync(r => r.Name == "Administrator");
                if (adminRole != null)
                {
                    adminUser = new Models.User
                    {
                        Username = "admin",
                        Email = "<EMAIL>",
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword("Admin@123"),
                        FirstName = "System",
                        LastName = "Administrator",
                        RoleId = adminRole.Id,
                        IsActive = true,
                        IsEmailVerified = true
                    };

                    context.Users.Add(adminUser);
                    await context.SaveChangesAsync();

                    logger.LogInformation("Default admin user created successfully");
                }
            }

            // Add more seed data as needed
        }
    }

    /// <summary>
    /// Database statistics model
    /// </summary>
    public class DatabaseStatistics
    {
        public long UserCount { get; set; }
        public long CourseCount { get; set; }
        public long TestCount { get; set; }
        public long QuestionCount { get; set; }
        public long TestResultCount { get; set; }
        public long AssignmentCount { get; set; }
        public long AttendanceCount { get; set; }
        public bool IsOnlineMode { get; set; }

        public override string ToString()
        {
            return $"DatabaseStats{{ Users={UserCount}, Courses={CourseCount}, Tests={TestCount}, " +
                   $"Questions={QuestionCount}, TestResults={TestResultCount}, Assignments={AssignmentCount}, " +
                   $"Attendance={AttendanceCount}, OnlineMode={IsOnlineMode} }}";
        }
    }
}
