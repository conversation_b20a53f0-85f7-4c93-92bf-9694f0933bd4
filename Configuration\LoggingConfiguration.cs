using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Events;
using Serilog.Formatting.Json;

namespace LMSCBTSystem.Configuration
{
    /// <summary>
    /// Logging configuration setup
    /// </summary>
    public static class LoggingConfiguration
    {
        /// <summary>
        /// Configure Serilog logging
        /// </summary>
        public static IHostBuilder ConfigureLogging(this IHostBuilder hostBuilder, IConfiguration config)
        {
            return hostBuilder.UseSerilog((context, services, loggerConfiguration) =>
            {
                loggerConfiguration
                    .ReadFrom.Configuration(context.Configuration)
                    .ReadFrom.Services(services)
                    .Enrich.FromLogContext()
                    .Enrich.WithProperty("Application", "LMS CBT System")
                    .Enrich.WithProperty("Version", "1.0.0")
                    .Enrich.WithMachineName()
                    .Enrich.WithEnvironmentUserName()
                    .WriteTo.Console(
                        outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}")
                    .WriteTo.File(
                        path: GetLogFilePath(config),
                        rollingInterval: GetRollingInterval(config),
                        retainedFileCountLimit: GetRetainedFileCount(config),
                        formatter: new JsonFormatter(),
                        restrictedToMinimumLevel: LogEventLevel.Information)
                    .WriteTo.File(
                        path: GetErrorLogFilePath(config),
                        rollingInterval: RollingInterval.Day,
                        retainedFileCountLimit: 30,
                        restrictedToMinimumLevel: LogEventLevel.Error);

                // Set minimum log level based on environment
                var logLevel = GetMinimumLogLevel(config);
                loggerConfiguration.MinimumLevel.Is(logLevel);

                // Override log levels for specific namespaces
                loggerConfiguration.MinimumLevel.Override("Microsoft", LogEventLevel.Warning);
                loggerConfiguration.MinimumLevel.Override("Microsoft.Hosting.Lifetime", LogEventLevel.Information);
                loggerConfiguration.MinimumLevel.Override("Microsoft.EntityFrameworkCore", LogEventLevel.Warning);
                loggerConfiguration.MinimumLevel.Override("System", LogEventLevel.Warning);

                #if DEBUG
                loggerConfiguration.MinimumLevel.Override("LMSCBTSystem", LogEventLevel.Debug);
                #endif
            });
        }

        /// <summary>
        /// Add custom logging services
        /// </summary>
        public static IServiceCollection AddCustomLogging(this IServiceCollection services, IConfiguration config)
        {
            // Add custom logger factory
            services.AddSingleton<ILoggerFactory, LoggerFactory>();

            // Add application logger
            services.AddSingleton<IApplicationLogger, ApplicationLogger>();

            // Add audit logger
            services.AddSingleton<IAuditLogger, AuditLogger>();

            // Add performance logger
            services.AddSingleton<IPerformanceLogger, PerformanceLogger>();

            return services;
        }

        /// <summary>
        /// Initialize logging directory
        /// </summary>
        public static void InitializeLoggingDirectory(IConfiguration config)
        {
            try
            {
                var logPath = GetLogFilePath(config);
                var logDirectory = Path.GetDirectoryName(logPath);

                if (!string.IsNullOrEmpty(logDirectory) && !Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }

                // Create initial log entry
                Log.Information("Logging system initialized. Log directory: {LogDirectory}", logDirectory);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to initialize logging directory: {ex.Message}");
            }
        }

        private static string GetLogFilePath(IConfiguration config)
        {
            var path = config["Logging:File:Path"] ?? "logs/lms-cbt-.log";
            var timestamp = DateTime.Now.ToString("yyyyMMdd");
            return path.Replace("-.", $"-{timestamp}.");
        }

        private static string GetErrorLogFilePath(IConfiguration config)
        {
            var path = config["Logging:File:Path"] ?? "logs/lms-cbt-.log";
            var timestamp = DateTime.Now.ToString("yyyyMMdd");
            return path.Replace("-.", $"-errors-{timestamp}.");
        }

        private static RollingInterval GetRollingInterval(IConfiguration config)
        {
            var interval = config["Logging:File:RollingInterval"] ?? "Day";
            return Enum.TryParse<RollingInterval>(interval, true, out var result) ? result : RollingInterval.Day;
        }

        private static int GetRetainedFileCount(IConfiguration config)
        {
            return config.GetValue<int>("Logging:File:RetainedFileCountLimit", 30);
        }

        private static LogEventLevel GetMinimumLogLevel(IConfiguration config)
        {
            var level = config["Logging:LogLevel:Default"] ?? "Information";
            return Enum.TryParse<LogEventLevel>(level, true, out var result) ? result : LogEventLevel.Information;
        }
    }

    /// <summary>
    /// Application logger interface
    /// </summary>
    public interface IApplicationLogger
    {
        void LogUserLogin(long userId, string username, string ipAddress);
        void LogUserLogout(long userId, string username);
        void LogUserRegistration(long userId, string username, string email);
        void LogPasswordChange(long userId, string username);
        void LogFailedLogin(string usernameOrEmail, string ipAddress);
        void LogSystemEvent(string eventType, string description, object? data = null);
        void LogError(string operation, Exception exception, object? context = null);
    }

    /// <summary>
    /// Application logger implementation
    /// </summary>
    public class ApplicationLogger : IApplicationLogger
    {
        private readonly ILogger<ApplicationLogger> _logger;

        public ApplicationLogger(ILogger<ApplicationLogger> logger)
        {
            _logger = logger;
        }

        public void LogUserLogin(long userId, string username, string ipAddress)
        {
            _logger.LogInformation("User login: {UserId} ({Username}) from {IpAddress}", userId, username, ipAddress);
        }

        public void LogUserLogout(long userId, string username)
        {
            _logger.LogInformation("User logout: {UserId} ({Username})", userId, username);
        }

        public void LogUserRegistration(long userId, string username, string email)
        {
            _logger.LogInformation("User registration: {UserId} ({Username}, {Email})", userId, username, email);
        }

        public void LogPasswordChange(long userId, string username)
        {
            _logger.LogInformation("Password changed: {UserId} ({Username})", userId, username);
        }

        public void LogFailedLogin(string usernameOrEmail, string ipAddress)
        {
            _logger.LogWarning("Failed login attempt: {UsernameOrEmail} from {IpAddress}", usernameOrEmail, ipAddress);
        }

        public void LogSystemEvent(string eventType, string description, object? data = null)
        {
            _logger.LogInformation("System event [{EventType}]: {Description} {Data}", eventType, description, data);
        }

        public void LogError(string operation, Exception exception, object? context = null)
        {
            _logger.LogError(exception, "Error in {Operation}: {Message} {Context}", operation, exception.Message, context);
        }
    }

    /// <summary>
    /// Audit logger interface
    /// </summary>
    public interface IAuditLogger
    {
        void LogDataAccess(long userId, string entityType, long entityId, string operation);
        void LogDataModification(long userId, string entityType, long entityId, string operation, object? oldValue = null, object? newValue = null);
        void LogPermissionCheck(long userId, string permission, bool granted);
        void LogConfigurationChange(long userId, string setting, object? oldValue, object? newValue);
    }

    /// <summary>
    /// Audit logger implementation
    /// </summary>
    public class AuditLogger : IAuditLogger
    {
        private readonly ILogger<AuditLogger> _logger;

        public AuditLogger(ILogger<AuditLogger> logger)
        {
            _logger = logger;
        }

        public void LogDataAccess(long userId, string entityType, long entityId, string operation)
        {
            _logger.LogInformation("Data access: User {UserId} {Operation} {EntityType}:{EntityId}", 
                userId, operation, entityType, entityId);
        }

        public void LogDataModification(long userId, string entityType, long entityId, string operation, object? oldValue = null, object? newValue = null)
        {
            _logger.LogInformation("Data modification: User {UserId} {Operation} {EntityType}:{EntityId} - Old: {OldValue}, New: {NewValue}", 
                userId, operation, entityType, entityId, oldValue, newValue);
        }

        public void LogPermissionCheck(long userId, string permission, bool granted)
        {
            _logger.LogInformation("Permission check: User {UserId} - {Permission}: {Granted}", 
                userId, permission, granted ? "GRANTED" : "DENIED");
        }

        public void LogConfigurationChange(long userId, string setting, object? oldValue, object? newValue)
        {
            _logger.LogInformation("Configuration change: User {UserId} changed {Setting} from {OldValue} to {NewValue}", 
                userId, setting, oldValue, newValue);
        }
    }

    /// <summary>
    /// Performance logger interface
    /// </summary>
    public interface IPerformanceLogger
    {
        void LogOperationDuration(string operation, TimeSpan duration, object? context = null);
        void LogDatabaseQuery(string query, TimeSpan duration, int recordCount = 0);
        void LogApiCall(string endpoint, TimeSpan duration, int statusCode);
        IDisposable BeginScope(string operation);
    }

    /// <summary>
    /// Performance logger implementation
    /// </summary>
    public class PerformanceLogger : IPerformanceLogger
    {
        private readonly ILogger<PerformanceLogger> _logger;

        public PerformanceLogger(ILogger<PerformanceLogger> logger)
        {
            _logger = logger;
        }

        public void LogOperationDuration(string operation, TimeSpan duration, object? context = null)
        {
            if (duration.TotalMilliseconds > 1000) // Log slow operations
            {
                _logger.LogWarning("Slow operation: {Operation} took {Duration}ms {Context}", 
                    operation, duration.TotalMilliseconds, context);
            }
            else
            {
                _logger.LogDebug("Operation: {Operation} took {Duration}ms {Context}", 
                    operation, duration.TotalMilliseconds, context);
            }
        }

        public void LogDatabaseQuery(string query, TimeSpan duration, int recordCount = 0)
        {
            _logger.LogDebug("Database query: {Query} took {Duration}ms, returned {RecordCount} records", 
                query, duration.TotalMilliseconds, recordCount);
        }

        public void LogApiCall(string endpoint, TimeSpan duration, int statusCode)
        {
            _logger.LogInformation("API call: {Endpoint} took {Duration}ms, status: {StatusCode}", 
                endpoint, duration.TotalMilliseconds, statusCode);
        }

        public IDisposable BeginScope(string operation)
        {
            return new PerformanceScope(Log.Logger, operation);
        }
    }

    /// <summary>
    /// Performance measurement scope
    /// </summary>
    public class PerformanceScope : IDisposable
    {
        private readonly Serilog.ILogger _logger;
        private readonly string _operation;
        private readonly DateTime _startTime;

        public PerformanceScope(Serilog.ILogger logger, string operation)
        {
            _logger = logger;
            _operation = operation;
            _startTime = DateTime.UtcNow;
        }

        public void Dispose()
        {
            var duration = DateTime.UtcNow - _startTime;
            _logger.Debug("Operation {Operation} completed in {Duration}ms", _operation, duration.TotalMilliseconds);
        }
    }
}
