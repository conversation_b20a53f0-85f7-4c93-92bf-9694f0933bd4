using Microsoft.EntityFrameworkCore;
using LMSCBTSystem.Models;

namespace LMSCBTSystem.Data
{
    /// <summary>
    /// Entity Framework Database Context for LMS CBT System
    /// </summary>
    public class LMSDbContext : DbContext
    {
        public LMSDbContext(DbContextOptions<LMSDbContext> options) : base(options)
        {
        }

        // DbSets for all entities
        public DbSet<User> Users { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }
        public DbSet<Course> Courses { get; set; }
        public DbSet<Test> Tests { get; set; }
        public DbSet<Question> Questions { get; set; }
        public DbSet<Answer> Answers { get; set; }
        public DbSet<TestResult> TestResults { get; set; }
        public DbSet<Assignment> Assignments { get; set; }
        public DbSet<Submission> Submissions { get; set; }
        public DbSet<Attendance> Attendances { get; set; }
        public DbSet<Grade> Grades { get; set; }
        public DbSet<Resource> Resources { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<SyncRecord> SyncRecords { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure entity relationships and constraints
            ConfigureUserEntity(modelBuilder);
            ConfigureCourseEntity(modelBuilder);
            ConfigureTestEntity(modelBuilder);
            ConfigureQuestionEntity(modelBuilder);
            ConfigureAnswerEntity(modelBuilder);
            ConfigureTestResultEntity(modelBuilder);
            ConfigureAssignmentEntity(modelBuilder);
            ConfigureSubmissionEntity(modelBuilder);
            ConfigureAttendanceEntity(modelBuilder);
            ConfigureGradeEntity(modelBuilder);
            ConfigureResourceEntity(modelBuilder);
            ConfigureNotificationEntity(modelBuilder);
            ConfigureSyncRecordEntity(modelBuilder);

            // Seed initial data
            SeedInitialData(modelBuilder);
        }

        private void ConfigureUserEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Email).IsUnique();
                entity.HasIndex(e => e.Username).IsUnique();
                entity.HasIndex(e => e.StudentId).IsUnique();
                
                entity.Property(e => e.Gender)
                    .HasConversion<string>();
                
                entity.HasOne(e => e.Role)
                    .WithMany(r => r.Users)
                    .HasForeignKey(e => e.RoleId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }

        private void ConfigureCourseEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Course>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.CourseCode).IsUnique();
                
                entity.Property(e => e.Status)
                    .HasConversion<string>();
                
                entity.HasOne(e => e.Teacher)
                    .WithMany()
                    .HasForeignKey(e => e.TeacherId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }

        private void ConfigureTestEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Test>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.Property(e => e.TestType)
                    .HasConversion<string>();
                    
                entity.Property(e => e.Status)
                    .HasConversion<string>();
                
                entity.HasOne(e => e.Course)
                    .WithMany(c => c.Tests)
                    .HasForeignKey(e => e.CourseId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }

        private void ConfigureQuestionEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Question>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.Property(e => e.QuestionType)
                    .HasConversion<string>();
                
                entity.HasOne(e => e.Test)
                    .WithMany(t => t.Questions)
                    .HasForeignKey(e => e.TestId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }

        private void ConfigureAnswerEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Answer>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.HasOne(e => e.Question)
                    .WithMany(q => q.Answers)
                    .HasForeignKey(e => e.QuestionId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }

        private void ConfigureTestResultEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<TestResult>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.Property(e => e.Status)
                    .HasConversion<string>();
                
                entity.HasOne(e => e.Test)
                    .WithMany(t => t.TestResults)
                    .HasForeignKey(e => e.TestId)
                    .OnDelete(DeleteBehavior.Cascade);
                    
                entity.HasOne(e => e.Student)
                    .WithMany()
                    .HasForeignKey(e => e.StudentId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }

        private void ConfigureAssignmentEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Assignment>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.Property(e => e.Status)
                    .HasConversion<string>();
                
                entity.HasOne(e => e.Course)
                    .WithMany(c => c.Assignments)
                    .HasForeignKey(e => e.CourseId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }

        private void ConfigureSubmissionEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Submission>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.Property(e => e.Status)
                    .HasConversion<string>();
                
                entity.HasOne(e => e.Assignment)
                    .WithMany(a => a.Submissions)
                    .HasForeignKey(e => e.AssignmentId)
                    .OnDelete(DeleteBehavior.Cascade);
                    
                entity.HasOne(e => e.Student)
                    .WithMany()
                    .HasForeignKey(e => e.StudentId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }

        private void ConfigureAttendanceEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Attendance>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.Property(e => e.Status)
                    .HasConversion<string>();
                
                entity.HasOne(e => e.Course)
                    .WithMany(c => c.Attendances)
                    .HasForeignKey(e => e.CourseId)
                    .OnDelete(DeleteBehavior.Cascade);
                    
                entity.HasOne(e => e.User)
                    .WithMany()
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }

        private void ConfigureGradeEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Grade>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.Property(e => e.GradeType)
                    .HasConversion<string>();
                
                entity.HasOne(e => e.Student)
                    .WithMany()
                    .HasForeignKey(e => e.StudentId)
                    .OnDelete(DeleteBehavior.Restrict);
                    
                entity.HasOne(e => e.Course)
                    .WithMany()
                    .HasForeignKey(e => e.CourseId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }

        private void ConfigureResourceEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Resource>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.Property(e => e.ResourceType)
                    .HasConversion<string>();
                
                entity.HasOne(e => e.Course)
                    .WithMany(c => c.Resources)
                    .HasForeignKey(e => e.CourseId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }

        private void ConfigureNotificationEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Notification>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.Property(e => e.NotificationType)
                    .HasConversion<string>();
                
                entity.HasOne(e => e.User)
                    .WithMany()
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }

        private void ConfigureSyncRecordEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<SyncRecord>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.Property(e => e.SyncType)
                    .HasConversion<string>();
                    
                entity.Property(e => e.Status)
                    .HasConversion<string>();
            });
        }

        private void SeedInitialData(ModelBuilder modelBuilder)
        {
            // Seed default roles
            modelBuilder.Entity<Role>().HasData(
                new Role { Id = 1, Name = "Administrator", Description = "System administrator with full access", CreatedAt = DateTime.UtcNow },
                new Role { Id = 2, Name = "Teacher", Description = "Teacher with course management access", CreatedAt = DateTime.UtcNow },
                new Role { Id = 3, Name = "Student", Description = "Student with learning access", CreatedAt = DateTime.UtcNow },
                new Role { Id = 4, Name = "Staff", Description = "Staff with limited administrative access", CreatedAt = DateTime.UtcNow }
            );
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            // Update timestamps for entities
            var entries = ChangeTracker.Entries<BaseEntity>();
            
            foreach (var entry in entries)
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Entity.CreatedAt = DateTime.UtcNow;
                        entry.Entity.UpdatedAt = DateTime.UtcNow;
                        break;
                    case EntityState.Modified:
                        entry.Entity.UpdatedAt = DateTime.UtcNow;
                        break;
                }
            }

            return await base.SaveChangesAsync(cancellationToken);
        }
    }
}
