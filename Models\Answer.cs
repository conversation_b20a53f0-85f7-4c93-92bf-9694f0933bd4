using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace LMSCBTSystem.Models
{
    /// <summary>
    /// Answer entity for storing student answers to questions
    /// </summary>
    [Table("answers")]
    [Index(nameof(QuestionId))]
    [Index(nameof(StudentId))]
    public class Answer : BaseEntity
    {
        [Required]
        [Column("question_id")]
        public long QuestionId { get; set; }

        [ForeignKey(nameof(QuestionId))]
        public virtual Question Question { get; set; } = null!;

        [Required]
        [Column("student_id")]
        public long StudentId { get; set; }

        [ForeignKey(nameof(StudentId))]
        public virtual User Student { get; set; } = null!;

        [Required]
        [Column("test_result_id")]
        public long TestResultId { get; set; }

        [ForeignKey(nameof(TestResultId))]
        public virtual TestResult TestResult { get; set; } = null!;

        [Column("answer_text", TypeName = "TEXT")]
        public string? AnswerText { get; set; }

        [Column("selected_options", TypeName = "TEXT")]
        public string? SelectedOptions { get; set; }

        [Required]
        [Column("is_correct")]
        public bool IsCorrect { get; set; } = false;

        [Required]
        [Column("marks_awarded")]
        public double MarksAwarded { get; set; } = 0;

        [Column("time_taken_seconds")]
        public int? TimeTakenSeconds { get; set; }

        [Column("answered_at")]
        public DateTime? AnsweredAt { get; set; }

        [Required]
        [Column("is_flagged")]
        public bool IsFlagged { get; set; } = false;

        [StringLength(500)]
        [Column("feedback")]
        public string? Feedback { get; set; }

        /// <summary>
        /// Get time taken as TimeSpan
        /// </summary>
        [NotMapped]
        public TimeSpan? TimeTaken => TimeTakenSeconds.HasValue ? TimeSpan.FromSeconds(TimeTakenSeconds.Value) : null;

        /// <summary>
        /// Calculate score percentage
        /// </summary>
        public double ScorePercentage => Question?.Marks > 0 ? (MarksAwarded / Question.Marks) * 100 : 0;
    }
}
