using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace LMSCBTSystem.Models
{
    /// <summary>
    /// Assignment entity for course assignments
    /// </summary>
    [Table("assignments")]
    [Index(nameof(CourseId))]
    [Index(nameof(Status))]
    [Index(nameof(DueDate))]
    public class Assignment : BaseEntity
    {
        [Required]
        [StringLength(200)]
        [Column("title")]
        public string Title { get; set; } = string.Empty;

        [StringLength(2000)]
        [Column("description")]
        public string? Description { get; set; }

        [Required]
        [Column("course_id")]
        public long CourseId { get; set; }

        [ForeignKey(nameof(CourseId))]
        public virtual Course Course { get; set; } = null!;

        [Required]
        [Column("assignment_type")]
        public AssignmentType AssignmentType { get; set; } = AssignmentType.Individual;

        [Required]
        [Column("submission_type")]
        public SubmissionType SubmissionType { get; set; } = SubmissionType.File;

        [Required]
        [Column("status")]
        public AssignmentStatus Status { get; set; } = AssignmentStatus.Draft;

        [Column("assigned_date")]
        public DateTime? AssignedDate { get; set; }

        [Column("due_date")]
        public DateTime? DueDate { get; set; }

        [Required]
        [Column("max_marks")]
        public double MaxMarks { get; set; } = 100;

        [Required]
        [Column("allow_late_submission")]
        public bool AllowLateSubmission { get; set; } = false;

        [Column("late_penalty_percentage")]
        public double? LatePenaltyPercentage { get; set; }

        [Required]
        [Column("max_file_size_mb")]
        public int MaxFileSizeMB { get; set; } = 10;

        [StringLength(500)]
        [Column("allowed_file_types")]
        public string? AllowedFileTypes { get; set; } = "pdf,doc,docx,txt";

        [StringLength(2000)]
        [Column("instructions")]
        public string? Instructions { get; set; }

        [StringLength(255)]
        [Column("attachment_url")]
        public string? AttachmentUrl { get; set; }

        [Required]
        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<Submission> Submissions { get; set; } = new List<Submission>();

        /// <summary>
        /// Check if assignment is currently available
        /// </summary>
        public bool IsAvailable
        {
            get
            {
                var now = DateTime.UtcNow;
                return IsActive && Status == AssignmentStatus.Published &&
                       (!AssignedDate.HasValue || now >= AssignedDate.Value) &&
                       (AllowLateSubmission || !DueDate.HasValue || now <= DueDate.Value);
            }
        }

        /// <summary>
        /// Check if assignment is overdue
        /// </summary>
        public bool IsOverdue => DueDate.HasValue && DateTime.UtcNow > DueDate.Value;

        /// <summary>
        /// Get remaining time until due date
        /// </summary>
        public TimeSpan? TimeUntilDue
        {
            get
            {
                if (!DueDate.HasValue) return null;
                var remaining = DueDate.Value - DateTime.UtcNow;
                return remaining.TotalSeconds > 0 ? remaining : TimeSpan.Zero;
            }
        }

        /// <summary>
        /// Get submission count
        /// </summary>
        public int SubmissionCount => Submissions.Count;

        /// <summary>
        /// Get graded submission count
        /// </summary>
        public int GradedSubmissionCount => Submissions.Count(s => s.Status == SubmissionStatus.Graded);

        /// <summary>
        /// Get average score
        /// </summary>
        public double AverageScore
        {
            get
            {
                var gradedSubmissions = Submissions.Where(s => s.Status == SubmissionStatus.Graded).ToList();
                return gradedSubmissions.Any() ? gradedSubmissions.Average(s => s.MarksObtained) : 0;
            }
        }

        /// <summary>
        /// Check if student has submitted
        /// </summary>
        public bool HasStudentSubmitted(long studentId)
        {
            return Submissions.Any(s => s.StudentId == studentId);
        }

        /// <summary>
        /// Get student's submission
        /// </summary>
        public Submission? GetStudentSubmission(long studentId)
        {
            return Submissions.FirstOrDefault(s => s.StudentId == studentId);
        }

        /// <summary>
        /// Calculate late penalty
        /// </summary>
        public double CalculateLatePenalty(DateTime submissionDate)
        {
            if (!DueDate.HasValue || submissionDate <= DueDate.Value || !LatePenaltyPercentage.HasValue)
                return 0;

            return LatePenaltyPercentage.Value;
        }
    }

    public enum AssignmentType
    {
        Individual,
        Group,
        Project,
        Research,
        Presentation
    }

    public enum SubmissionType
    {
        File,
        Text,
        Link,
        Video,
        Audio
    }

    public enum AssignmentStatus
    {
        Draft,
        Published,
        Active,
        Completed,
        Cancelled
    }
}
