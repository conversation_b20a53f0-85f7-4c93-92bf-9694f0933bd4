using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace LMSCBTSystem.Models
{
    /// <summary>
    /// Attendance entity for tracking student attendance
    /// </summary>
    [Table("attendance")]
    [Index(nameof(CourseId))]
    [Index(nameof(UserId))]
    [Index(nameof(AttendanceDate))]
    public class Attendance : BaseEntity
    {
        [Required]
        [Column("course_id")]
        public long CourseId { get; set; }

        [ForeignKey(nameof(CourseId))]
        public virtual Course Course { get; set; } = null!;

        [Required]
        [Column("user_id")]
        public long UserId { get; set; }

        [ForeignKey(nameof(UserId))]
        public virtual User User { get; set; } = null!;

        // Alias for compatibility
        public virtual User Student => User;

        [Required]
        [Column("attendance_date")]
        public DateTime AttendanceDate { get; set; }

        [Required]
        [Column("attendance_type")]
        public AttendanceType AttendanceType { get; set; } = AttendanceType.Class;

        [Required]
        [Column("status")]
        public AttendanceStatus Status { get; set; } = AttendanceStatus.Present;

        [Column("check_in_time")]
        public DateTime? CheckInTime { get; set; }

        [Column("check_out_time")]
        public DateTime? CheckOutTime { get; set; }

        [StringLength(500)]
        [Column("notes")]
        public string? Notes { get; set; }

        [Column("marked_by")]
        public long? MarkedBy { get; set; }

        [ForeignKey(nameof(MarkedBy))]
        public virtual User? MarkedByUser { get; set; }

        [Column("marked_at")]
        public DateTime? MarkedAt { get; set; }

        [Required]
        [Column("is_excused")]
        public bool IsExcused { get; set; } = false;

        [StringLength(500)]
        [Column("excuse_reason")]
        public string? ExcuseReason { get; set; }

        /// <summary>
        /// Get duration of attendance
        /// </summary>
        [NotMapped]
        public TimeSpan? Duration => CheckInTime.HasValue && CheckOutTime.HasValue 
            ? CheckOutTime.Value - CheckInTime.Value 
            : null;

        /// <summary>
        /// Check if student was present
        /// </summary>
        public bool IsPresent => Status == AttendanceStatus.Present;

        /// <summary>
        /// Check if student was absent
        /// </summary>
        public bool IsAbsent => Status == AttendanceStatus.Absent;

        /// <summary>
        /// Check if student was late
        /// </summary>
        public bool IsLate => Status == AttendanceStatus.Late;

        /// <summary>
        /// Mark attendance as present
        /// </summary>
        public void MarkPresent(long markedBy, DateTime? checkInTime = null)
        {
            Status = AttendanceStatus.Present;
            CheckInTime = checkInTime ?? DateTime.UtcNow;
            MarkedBy = markedBy;
            MarkedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Mark attendance as absent
        /// </summary>
        public void MarkAbsent(long markedBy, string? reason = null)
        {
            Status = AttendanceStatus.Absent;
            Notes = reason;
            MarkedBy = markedBy;
            MarkedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Mark attendance as late
        /// </summary>
        public void MarkLate(long markedBy, DateTime checkInTime, string? reason = null)
        {
            Status = AttendanceStatus.Late;
            CheckInTime = checkInTime;
            Notes = reason;
            MarkedBy = markedBy;
            MarkedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Excuse the absence
        /// </summary>
        public void ExcuseAbsence(string reason)
        {
            IsExcused = true;
            ExcuseReason = reason;
        }

        /// <summary>
        /// Check out
        /// </summary>
        public void CheckOut(DateTime? checkOutTime = null)
        {
            CheckOutTime = checkOutTime ?? DateTime.UtcNow;
        }
    }

    public enum AttendanceType
    {
        Class,
        Lab,
        Exam,
        Event,
        Meeting
    }

    public enum AttendanceStatus
    {
        Present,
        Absent,
        Late,
        Excused,
        PartiallyPresent
    }
}
