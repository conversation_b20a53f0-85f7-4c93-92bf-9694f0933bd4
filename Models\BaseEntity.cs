using System.ComponentModel.DataAnnotations;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace LMSCBTSystem.Models
{
    /// <summary>
    /// Base entity class with common fields for all entities
    /// Provides audit fields and common functionality
    /// </summary>
    public abstract class BaseEntity
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long Id { get; set; }

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Required]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public long? CreatedBy { get; set; }

        public long? UpdatedBy { get; set; }

        [Required]
        public bool IsDeleted { get; set; } = false;

        [Timestamp]
        public byte[]? Version { get; set; }

        /// <summary>
        /// Mark entity as deleted (soft delete)
        /// </summary>
        public virtual void Delete()
        {
            IsDeleted = true;
            UpdatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Update the UpdatedAt timestamp
        /// </summary>
        public virtual void Touch()
        {
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
