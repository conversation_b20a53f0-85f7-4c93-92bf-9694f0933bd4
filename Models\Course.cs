using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace LMSCBTSystem.Models
{
    /// <summary>
    /// Course entity representing academic courses in the LMS
    /// Contains course information, enrollment, and relationships
    /// </summary>
    [Table("courses")]
    [Index(nameof(CourseCode), IsUnique = true)]
    [Index(nameof(Status))]
    public class Course : BaseEntity
    {
        [Required]
        [StringLength(20)]
        [Column("course_code")]
        public string CourseCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        [Column("name")]
        public string Name { get; set; } = string.Empty;

        [StringLength(1000)]
        [Column("description")]
        public string? Description { get; set; }

        [Required]
        [Column("teacher_id")]
        public long TeacherId { get; set; }

        [ForeignKey(nameof(TeacherId))]
        public virtual User Teacher { get; set; } = null!;

        [Column("credits")]
        public int? Credits { get; set; }

        [Column("start_date")]
        public DateTime? StartDate { get; set; }

        [Column("end_date")]
        public DateTime? EndDate { get; set; }

        [Required]
        [Column("status")]
        public CourseStatus Status { get; set; } = CourseStatus.Draft;

        [Column("max_enrollment")]
        public int? MaxEnrollment { get; set; }

        [Required]
        [Column("current_enrollment")]
        public int CurrentEnrollment { get; set; } = 0;

        [StringLength(50)]
        [Column("semester")]
        public string? Semester { get; set; }

        [StringLength(10)]
        [Column("academic_year")]
        public string? AcademicYear { get; set; }

        [StringLength(100)]
        [Column("department")]
        public string? Department { get; set; }

        [Required]
        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        [Required]
        [Column("allow_self_enrollment")]
        public bool AllowSelfEnrollment { get; set; } = false;

        [StringLength(255)]
        [Column("course_image_url")]
        public string? CourseImageUrl { get; set; }

        // Navigation properties
        public virtual ICollection<User> EnrolledStudents { get; set; } = new List<User>();
        public virtual ICollection<Test> Tests { get; set; } = new List<Test>();
        public virtual ICollection<Assignment> Assignments { get; set; } = new List<Assignment>();
        public virtual ICollection<Resource> Resources { get; set; } = new List<Resource>();
        public virtual ICollection<Attendance> AttendanceRecords { get; set; } = new List<Attendance>();
        public virtual ICollection<Attendance> Attendances { get; set; } = new List<Attendance>();

        /// <summary>
        /// Check if course is full
        /// </summary>
        public bool IsFull => MaxEnrollment.HasValue && CurrentEnrollment >= MaxEnrollment.Value;

        /// <summary>
        /// Check if course is currently active
        /// </summary>
        public bool IsCurrentlyActive => IsActive && Status == CourseStatus.Active;

        /// <summary>
        /// Check if enrollment is open
        /// </summary>
        public bool IsEnrollmentOpen => IsCurrentlyActive && AllowSelfEnrollment && !IsFull;

        /// <summary>
        /// Get enrollment percentage
        /// </summary>
        public double EnrollmentPercentage => MaxEnrollment.HasValue && MaxEnrollment.Value > 0 
            ? (double)CurrentEnrollment / MaxEnrollment.Value * 100 
            : 0;

        /// <summary>
        /// Check if course is in session
        /// </summary>
        public bool IsInSession
        {
            get
            {
                var now = DateTime.Now.Date;
                return StartDate.HasValue && EndDate.HasValue 
                    && now >= StartDate.Value.Date && now <= EndDate.Value.Date;
            }
        }

        /// <summary>
        /// Get course duration in days
        /// </summary>
        public int? DurationInDays => StartDate.HasValue && EndDate.HasValue 
            ? (int)(EndDate.Value - StartDate.Value).TotalDays 
            : null;

        /// <summary>
        /// Enroll a student in the course
        /// </summary>
        public bool EnrollStudent(User student)
        {
            if (!IsEnrollmentOpen || EnrolledStudents.Contains(student))
                return false;

            EnrolledStudents.Add(student);
            CurrentEnrollment++;
            return true;
        }

        /// <summary>
        /// Unenroll a student from the course
        /// </summary>
        public bool UnenrollStudent(User student)
        {
            if (!EnrolledStudents.Contains(student))
                return false;

            EnrolledStudents.Remove(student);
            CurrentEnrollment = Math.Max(0, CurrentEnrollment - 1);
            return true;
        }

        /// <summary>
        /// Check if student is enrolled
        /// </summary>
        public bool IsStudentEnrolled(User student)
        {
            return EnrolledStudents.Contains(student);
        }

        /// <summary>
        /// Check if student is enrolled by ID
        /// </summary>
        public bool IsStudentEnrolled(long studentId)
        {
            return EnrolledStudents.Any(s => s.Id == studentId);
        }
    }

    public enum CourseStatus
    {
        Draft,
        Active,
        Inactive,
        Completed,
        Cancelled
    }
}
