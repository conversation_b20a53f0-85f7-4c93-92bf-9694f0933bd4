using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace LMSCBTSystem.Models
{
    /// <summary>
    /// Grade entity for storing student grades
    /// </summary>
    [Table("grades")]
    [Index(nameof(CourseId))]
    [Index(nameof(StudentId))]
    [Index(nameof(GradeType))]
    public class Grade : BaseEntity
    {
        [Required]
        [Column("course_id")]
        public long CourseId { get; set; }

        [ForeignKey(nameof(CourseId))]
        public virtual Course Course { get; set; } = null!;

        [Required]
        [Column("student_id")]
        public long StudentId { get; set; }

        [ForeignKey(nameof(StudentId))]
        public virtual User Student { get; set; } = null!;

        [Required]
        [Column("grade_type")]
        public GradeType GradeType { get; set; } = GradeType.Assignment;

        [Column("test_id")]
        public long? TestId { get; set; }

        [ForeignKey(nameof(TestId))]
        public virtual Test? Test { get; set; }

        [Column("assignment_id")]
        public long? AssignmentId { get; set; }

        [ForeignKey(nameof(AssignmentId))]
        public virtual Assignment? Assignment { get; set; }

        [Required]
        [StringLength(100)]
        [Column("grade_item")]
        public string GradeItem { get; set; } = string.Empty;

        [Required]
        [Column("points_earned")]
        public double PointsEarned { get; set; } = 0;

        [Required]
        [Column("points_possible")]
        public double PointsPossible { get; set; } = 100;

        [Required]
        [Column("percentage")]
        public double Percentage { get; set; } = 0;

        [StringLength(10)]
        [Column("letter_grade")]
        public string? LetterGrade { get; set; }

        [Column("gpa_points")]
        public double? GpaPoints { get; set; }

        [StringLength(1000)]
        [Column("comments")]
        public string? Comments { get; set; }

        [Column("graded_by")]
        public long? GradedBy { get; set; }

        [ForeignKey(nameof(GradedBy))]
        public virtual User? Grader { get; set; }

        [Column("graded_at")]
        public DateTime? GradedAt { get; set; }

        [Required]
        [Column("is_final")]
        public bool IsFinal { get; set; } = false;

        [Required]
        [Column("is_published")]
        public bool IsPublished { get; set; } = false;

        [Column("weight")]
        public double? Weight { get; set; }

        /// <summary>
        /// Calculate percentage from points
        /// </summary>
        public void CalculatePercentage()
        {
            Percentage = PointsPossible > 0 ? (PointsEarned / PointsPossible) * 100 : 0;
        }

        /// <summary>
        /// Calculate letter grade from percentage
        /// </summary>
        public void CalculateLetterGrade()
        {
            LetterGrade = Percentage switch
            {
                >= 97 => "A+",
                >= 93 => "A",
                >= 90 => "A-",
                >= 87 => "B+",
                >= 83 => "B",
                >= 80 => "B-",
                >= 77 => "C+",
                >= 73 => "C",
                >= 70 => "C-",
                >= 67 => "D+",
                >= 63 => "D",
                >= 60 => "D-",
                _ => "F"
            };
        }

        /// <summary>
        /// Calculate GPA points from letter grade
        /// </summary>
        public void CalculateGpaPoints()
        {
            GpaPoints = LetterGrade switch
            {
                "A+" => 4.0,
                "A" => 4.0,
                "A-" => 3.7,
                "B+" => 3.3,
                "B" => 3.0,
                "B-" => 2.7,
                "C+" => 2.3,
                "C" => 2.0,
                "C-" => 1.7,
                "D+" => 1.3,
                "D" => 1.0,
                "D-" => 0.7,
                "F" => 0.0,
                _ => 0.0
            };
        }

        /// <summary>
        /// Update grade with new points
        /// </summary>
        public void UpdateGrade(double pointsEarned, double pointsPossible, long graderId, string? comments = null)
        {
            PointsEarned = pointsEarned;
            PointsPossible = pointsPossible;
            Comments = comments;
            GradedBy = graderId;
            GradedAt = DateTime.UtcNow;

            CalculatePercentage();
            CalculateLetterGrade();
            CalculateGpaPoints();
        }

        /// <summary>
        /// Publish the grade
        /// </summary>
        public void Publish()
        {
            IsPublished = true;
        }

        /// <summary>
        /// Finalize the grade
        /// </summary>
        public void FinalizeGrade()
        {
            IsFinal = true;
            IsPublished = true;
        }

        /// <summary>
        /// Check if grade is passing
        /// </summary>
        public bool IsPassing => Percentage >= 60; // Assuming 60% is passing

        /// <summary>
        /// Get weighted points
        /// </summary>
        public double WeightedPoints => Weight.HasValue ? PointsEarned * Weight.Value : PointsEarned;
    }

    public enum GradeType
    {
        Assignment,
        Test,
        Quiz,
        Exam,
        Project,
        Participation,
        Final,
        Midterm,
        Lab,
        Homework
    }
}
