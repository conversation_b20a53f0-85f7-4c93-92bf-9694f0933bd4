using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace LMSCBTSystem.Models
{
    /// <summary>
    /// Notification entity for system notifications
    /// </summary>
    [Table("notifications")]
    [Index(nameof(UserId))]
    [Index(nameof(NotificationType))]
    [Index(nameof(IsRead))]
    public class Notification : BaseEntity
    {
        [Required]
        [Column("user_id")]
        public long UserId { get; set; }

        [ForeignKey(nameof(UserId))]
        public virtual User User { get; set; } = null!;

        [Required]
        [StringLength(200)]
        [Column("title")]
        public string Title { get; set; } = string.Empty;

        [Required]
        [StringLength(1000)]
        [Column("message")]
        public string Message { get; set; } = string.Empty;

        [Required]
        [Column("notification_type")]
        public NotificationType NotificationType { get; set; } = NotificationType.Info;

        [Required]
        [Column("priority")]
        public Priority Priority { get; set; } = Priority.Normal;

        [Required]
        [Column("is_read")]
        public bool IsRead { get; set; } = false;

        [Column("read_at")]
        public DateTime? ReadAt { get; set; }

        [StringLength(255)]
        [Column("action_url")]
        public string? ActionUrl { get; set; }

        [StringLength(100)]
        [Column("action_text")]
        public string? ActionText { get; set; }

        [Column("expires_at")]
        public DateTime? ExpiresAt { get; set; }

        [Column("sent_by")]
        public long? SentBy { get; set; }

        [ForeignKey(nameof(SentBy))]
        public virtual User? Sender { get; set; }

        [Column("related_entity_type")]
        public string? RelatedEntityType { get; set; }

        [Column("related_entity_id")]
        public long? RelatedEntityId { get; set; }

        /// <summary>
        /// Check if notification is expired
        /// </summary>
        public bool IsExpired => ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;

        /// <summary>
        /// Check if notification is active
        /// </summary>
        public bool IsActive => !IsExpired;

        /// <summary>
        /// Mark notification as read
        /// </summary>
        public void MarkAsRead()
        {
            if (!IsRead)
            {
                IsRead = true;
                ReadAt = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Mark notification as unread
        /// </summary>
        public void MarkAsUnread()
        {
            IsRead = false;
            ReadAt = null;
        }

        /// <summary>
        /// Get time since creation
        /// </summary>
        [NotMapped]
        public TimeSpan TimeSinceCreated => DateTime.UtcNow - CreatedAt;

        /// <summary>
        /// Get formatted time ago
        /// </summary>
        [NotMapped]
        public string TimeAgo
        {
            get
            {
                var timeSpan = TimeSinceCreated;
                
                if (timeSpan.TotalDays >= 1)
                    return $"{(int)timeSpan.TotalDays} day{((int)timeSpan.TotalDays != 1 ? "s" : "")} ago";
                
                if (timeSpan.TotalHours >= 1)
                    return $"{(int)timeSpan.TotalHours} hour{((int)timeSpan.TotalHours != 1 ? "s" : "")} ago";
                
                if (timeSpan.TotalMinutes >= 1)
                    return $"{(int)timeSpan.TotalMinutes} minute{((int)timeSpan.TotalMinutes != 1 ? "s" : "")} ago";
                
                return "Just now";
            }
        }

        /// <summary>
        /// Get CSS class for notification type
        /// </summary>
        [NotMapped]
        public string CssClass => NotificationType switch
        {
            NotificationType.Success => "notification-success",
            NotificationType.Warning => "notification-warning",
            NotificationType.Error => "notification-error",
            NotificationType.Info => "notification-info",
            _ => "notification-default"
        };

        /// <summary>
        /// Get icon for notification type
        /// </summary>
        [NotMapped]
        public string Icon => NotificationType switch
        {
            NotificationType.Success => "✓",
            NotificationType.Warning => "⚠",
            NotificationType.Error => "✗",
            NotificationType.Info => "ℹ",
            _ => "•"
        };
    }

    public enum NotificationType
    {
        Info,
        Success,
        Warning,
        Error,
        Assignment,
        Test,
        Grade,
        Course,
        System,
        Reminder
    }

    public enum Priority
    {
        Low,
        Normal,
        High,
        Urgent
    }
}
