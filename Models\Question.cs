using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace LMSCBTSystem.Models
{
    /// <summary>
    /// Question entity for test questions
    /// Supports multiple question types and configurations
    /// </summary>
    [Table("questions")]
    [Index(nameof(TestId))]
    [Index(nameof(QuestionType))]
    public class Question : BaseEntity
    {
        [Required]
        [Column("test_id")]
        public long TestId { get; set; }

        [ForeignKey(nameof(TestId))]
        public virtual Test Test { get; set; } = null!;

        [Required]
        [StringLength(2000)]
        [Column("question_text")]
        public string QuestionText { get; set; } = string.Empty;

        [Required]
        [Column("question_type")]
        public QuestionType QuestionType { get; set; } = QuestionType.MultipleChoice;

        [Required]
        [Column("marks")]
        public double Marks { get; set; } = 1.0;

        [Required]
        [Column("difficulty_level")]
        public DifficultyLevel DifficultyLevel { get; set; } = DifficultyLevel.Medium;

        [Required]
        [Column("order_index")]
        public int OrderIndex { get; set; }

        [StringLength(1000)]
        [Column("explanation")]
        public string? Explanation { get; set; }

        [StringLength(255)]
        [Column("image_url")]
        public string? ImageUrl { get; set; }

        [StringLength(255)]
        [Column("audio_url")]
        public string? AudioUrl { get; set; }

        [StringLength(255)]
        [Column("video_url")]
        public string? VideoUrl { get; set; }

        [Required]
        [Column("is_required")]
        public bool IsRequired { get; set; } = true;

        [Required]
        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        [Column("time_limit_seconds")]
        public int? TimeLimitSeconds { get; set; }

        // Store options as JSON for flexibility
        [Column("options_json", TypeName = "TEXT")]
        public string? OptionsJson { get; set; }

        // Store correct answers as JSON
        [Column("correct_answers_json", TypeName = "TEXT")]
        public string? CorrectAnswersJson { get; set; }

        // Navigation properties
        public virtual ICollection<Answer> Answers { get; set; } = new List<Answer>();

        /// <summary>
        /// Get question options
        /// </summary>
        [NotMapped]
        public List<QuestionOption> Options
        {
            get => string.IsNullOrEmpty(OptionsJson) 
                ? new List<QuestionOption>() 
                : JsonConvert.DeserializeObject<List<QuestionOption>>(OptionsJson) ?? new List<QuestionOption>();
            set => OptionsJson = JsonConvert.SerializeObject(value);
        }

        /// <summary>
        /// Get correct answers
        /// </summary>
        [NotMapped]
        public List<string> CorrectAnswers
        {
            get => string.IsNullOrEmpty(CorrectAnswersJson) 
                ? new List<string>() 
                : JsonConvert.DeserializeObject<List<string>>(CorrectAnswersJson) ?? new List<string>();
            set => CorrectAnswersJson = JsonConvert.SerializeObject(value);
        }

        /// <summary>
        /// Check if answer is correct
        /// </summary>
        public bool IsAnswerCorrect(string answer)
        {
            if (string.IsNullOrEmpty(answer)) return false;

            return QuestionType switch
            {
                QuestionType.MultipleChoice => CorrectAnswers.Contains(answer, StringComparer.OrdinalIgnoreCase),
                QuestionType.MultipleSelect => CorrectAnswers.All(ca => answer.Contains(ca, StringComparison.OrdinalIgnoreCase)),
                QuestionType.TrueFalse => CorrectAnswers.FirstOrDefault()?.Equals(answer, StringComparison.OrdinalIgnoreCase) == true,
                QuestionType.ShortAnswer => CorrectAnswers.Any(ca => ca.Equals(answer, StringComparison.OrdinalIgnoreCase)),
                QuestionType.Essay => false, // Essays need manual grading
                QuestionType.FillInTheBlank => CheckFillInTheBlankAnswer(answer),
                _ => false
            };
        }

        /// <summary>
        /// Check fill-in-the-blank answer
        /// </summary>
        private bool CheckFillInTheBlankAnswer(string answer)
        {
            var answerParts = answer.Split('|');
            var correctParts = CorrectAnswers;

            if (answerParts.Length != correctParts.Count) return false;

            for (int i = 0; i < answerParts.Length; i++)
            {
                if (!correctParts[i].Equals(answerParts[i].Trim(), StringComparison.OrdinalIgnoreCase))
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Calculate score for the answer
        /// </summary>
        public double CalculateScore(string answer)
        {
            if (QuestionType == QuestionType.Essay)
                return 0; // Manual grading required

            return IsAnswerCorrect(answer) ? Marks : 0;
        }

        /// <summary>
        /// Get question difficulty multiplier
        /// </summary>
        public double DifficultyMultiplier => DifficultyLevel switch
        {
            DifficultyLevel.Easy => 0.8,
            DifficultyLevel.Medium => 1.0,
            DifficultyLevel.Hard => 1.2,
            DifficultyLevel.Expert => 1.5,
            _ => 1.0
        };

        /// <summary>
        /// Get adjusted marks based on difficulty
        /// </summary>
        public double AdjustedMarks => Marks * DifficultyMultiplier;
    }

    public enum QuestionType
    {
        MultipleChoice,
        MultipleSelect,
        TrueFalse,
        ShortAnswer,
        Essay,
        FillInTheBlank,
        Matching,
        Ordering
    }

    public enum DifficultyLevel
    {
        Easy,
        Medium,
        Hard,
        Expert
    }

    public class QuestionOption
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Text { get; set; } = string.Empty;
        public string? ImageUrl { get; set; }
        public bool IsCorrect { get; set; } = false;
        public int OrderIndex { get; set; }
    }
}
