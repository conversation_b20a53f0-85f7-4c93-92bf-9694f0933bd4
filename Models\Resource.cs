using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace LMSCBTSystem.Models
{
    /// <summary>
    /// Resource entity for course resources and materials
    /// </summary>
    [Table("resources")]
    [Index(nameof(CourseId))]
    [Index(nameof(ResourceType))]
    public class Resource : BaseEntity
    {
        [Required]
        [StringLength(200)]
        [Column("title")]
        public string Title { get; set; } = string.Empty;

        [StringLength(1000)]
        [Column("description")]
        public string? Description { get; set; }

        [Required]
        [Column("course_id")]
        public long CourseId { get; set; }

        [ForeignKey(nameof(CourseId))]
        public virtual Course Course { get; set; } = null!;

        [Required]
        [Column("resource_type")]
        public ResourceType ResourceType { get; set; } = ResourceType.Document;

        [StringLength(255)]
        [Column("file_url")]
        public string? FileUrl { get; set; }

        [StringLength(255)]
        [Column("external_url")]
        public string? ExternalUrl { get; set; }

        [StringLength(100)]
        [Column("file_name")]
        public string? FileName { get; set; }

        [Column("file_size_bytes")]
        public long? FileSizeBytes { get; set; }

        [StringLength(50)]
        [Column("file_type")]
        public string? FileType { get; set; }

        [Column("content", TypeName = "TEXT")]
        public string? Content { get; set; }

        [Required]
        [Column("is_public")]
        public bool IsPublic { get; set; } = true;

        [Required]
        [Column("is_downloadable")]
        public bool IsDownloadable { get; set; } = true;

        [Required]
        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        [Column("order_index")]
        public int? OrderIndex { get; set; }

        [Column("uploaded_by")]
        public long? UploadedBy { get; set; }

        [ForeignKey(nameof(UploadedBy))]
        public virtual User? UploadedByUser { get; set; }

        [Column("download_count")]
        public int DownloadCount { get; set; } = 0;

        [Column("view_count")]
        public int ViewCount { get; set; } = 0;

        [StringLength(500)]
        [Column("tags")]
        public string? Tags { get; set; }

        /// <summary>
        /// Get file size in human readable format
        /// </summary>
        [NotMapped]
        public string FileSizeFormatted
        {
            get
            {
                if (!FileSizeBytes.HasValue) return "Unknown";

                var size = FileSizeBytes.Value;
                string[] sizes = { "B", "KB", "MB", "GB" };
                int order = 0;
                while (size >= 1024 && order < sizes.Length - 1)
                {
                    order++;
                    size = size / 1024;
                }
                return $"{size:0.##} {sizes[order]}";
            }
        }

        /// <summary>
        /// Get resource URL (file or external)
        /// </summary>
        [NotMapped]
        public string? ResourceUrl => !string.IsNullOrEmpty(FileUrl) ? FileUrl : ExternalUrl;

        /// <summary>
        /// Check if resource is a file
        /// </summary>
        public bool IsFile => !string.IsNullOrEmpty(FileUrl);

        /// <summary>
        /// Check if resource is external link
        /// </summary>
        public bool IsExternalLink => !string.IsNullOrEmpty(ExternalUrl);

        /// <summary>
        /// Get tags as list
        /// </summary>
        [NotMapped]
        public List<string> TagList
        {
            get => string.IsNullOrEmpty(Tags) 
                ? new List<string>() 
                : Tags.Split(',', StringSplitOptions.RemoveEmptyEntries).Select(t => t.Trim()).ToList();
            set => Tags = string.Join(",", value);
        }

        /// <summary>
        /// Increment download count
        /// </summary>
        public void IncrementDownloadCount()
        {
            DownloadCount++;
        }

        /// <summary>
        /// Increment view count
        /// </summary>
        public void IncrementViewCount()
        {
            ViewCount++;
        }

        /// <summary>
        /// Check if user can access resource
        /// </summary>
        public bool CanUserAccess(User user)
        {
            if (!IsActive) return false;
            if (IsPublic) return true;

            // Check if user is the teacher (enrollment check temporarily disabled)
            return Course.TeacherId == user.Id;
        }

        /// <summary>
        /// Get file extension
        /// </summary>
        [NotMapped]
        public string? FileExtension
        {
            get
            {
                if (string.IsNullOrEmpty(FileName)) return null;
                var lastDot = FileName.LastIndexOf('.');
                return lastDot >= 0 ? FileName.Substring(lastDot + 1).ToLowerInvariant() : null;
            }
        }

        /// <summary>
        /// Check if resource is an image
        /// </summary>
        public bool IsImage
        {
            get
            {
                var imageExtensions = new[] { "jpg", "jpeg", "png", "gif", "bmp", "svg", "webp" };
                return !string.IsNullOrEmpty(FileExtension) && imageExtensions.Contains(FileExtension);
            }
        }

        /// <summary>
        /// Check if resource is a video
        /// </summary>
        public bool IsVideo
        {
            get
            {
                var videoExtensions = new[] { "mp4", "avi", "mov", "wmv", "flv", "webm", "mkv" };
                return !string.IsNullOrEmpty(FileExtension) && videoExtensions.Contains(FileExtension);
            }
        }

        /// <summary>
        /// Check if resource is an audio file
        /// </summary>
        public bool IsAudio
        {
            get
            {
                var audioExtensions = new[] { "mp3", "wav", "ogg", "flac", "aac", "wma" };
                return !string.IsNullOrEmpty(FileExtension) && audioExtensions.Contains(FileExtension);
            }
        }
    }

    public enum ResourceType
    {
        Document,
        Video,
        Audio,
        Image,
        Link,
        Presentation,
        Spreadsheet,
        Archive,
        Code,
        Other
    }
}
