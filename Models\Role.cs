using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace LMSCBTSystem.Models
{
    /// <summary>
    /// Role entity for role-based access control (RBAC)
    /// Defines user roles and their permissions
    /// </summary>
    [Table("roles")]
    [Index(nameof(Name), IsUnique = true)]
    public class Role : BaseEntity
    {
        [Required]
        [StringLength(50)]
        [Column("name")]
        public string Name { get; set; } = string.Empty;

        [StringLength(255)]
        [Column("description")]
        public string? Description { get; set; }

        [Required]
        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<User> Users { get; set; } = new List<User>();
        public virtual ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();

        /// <summary>
        /// Get all permissions for this role
        /// </summary>
        public IEnumerable<string> Permissions => RolePermissions.Select(rp => rp.Permission);

        /// <summary>
        /// Check if role has specific permission
        /// </summary>
        public bool HasPermission(string permission)
        {
            return RolePermissions.Any(rp => rp.Permission.Equals(permission, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Add permission to role
        /// </summary>
        public void AddPermission(string permission)
        {
            if (!HasPermission(permission))
            {
                RolePermissions.Add(new RolePermission { RoleId = Id, Permission = permission });
            }
        }

        /// <summary>
        /// Remove permission from role
        /// </summary>
        public void RemovePermission(string permission)
        {
            var rolePermission = RolePermissions.FirstOrDefault(rp => 
                rp.Permission.Equals(permission, StringComparison.OrdinalIgnoreCase));
            if (rolePermission != null)
            {
                RolePermissions.Remove(rolePermission);
            }
        }
    }

    /// <summary>
    /// Role permission entity for storing role permissions
    /// </summary>
    [Table("role_permissions")]
    public class RolePermission
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long Id { get; set; }

        [Required]
        [Column("role_id")]
        public long RoleId { get; set; }

        [ForeignKey(nameof(RoleId))]
        public virtual Role Role { get; set; } = null!;

        [Required]
        [StringLength(100)]
        [Column("permission")]
        public string Permission { get; set; } = string.Empty;
    }

    /// <summary>
    /// Common system permissions
    /// </summary>
    public static class Permissions
    {
        // User management
        public const string USER_CREATE = "user.create";
        public const string USER_READ = "user.read";
        public const string USER_UPDATE = "user.update";
        public const string USER_DELETE = "user.delete";

        // Course management
        public const string COURSE_CREATE = "course.create";
        public const string COURSE_READ = "course.read";
        public const string COURSE_UPDATE = "course.update";
        public const string COURSE_DELETE = "course.delete";
        public const string COURSE_ENROLL = "course.enroll";

        // Test management
        public const string TEST_CREATE = "test.create";
        public const string TEST_READ = "test.read";
        public const string TEST_UPDATE = "test.update";
        public const string TEST_DELETE = "test.delete";
        public const string TEST_TAKE = "test.take";
        public const string TEST_GRADE = "test.grade";

        // System administration
        public const string SYSTEM_ADMIN = "system.admin";
        public const string SYSTEM_CONFIG = "system.config";
        public const string SYSTEM_BACKUP = "system.backup";
        public const string SYSTEM_REPORTS = "system.reports";

        // Data access
        public const string DATA_EXPORT = "data.export";
        public const string DATA_IMPORT = "data.import";
    }
}
