using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace LMSCBTSystem.Models
{
    /// <summary>
    /// Submission entity for assignment submissions
    /// </summary>
    [Table("submissions")]
    [Index(nameof(AssignmentId))]
    [Index(nameof(StudentId))]
    [Index(nameof(Status))]
    public class Submission : BaseEntity
    {
        [Required]
        [Column("assignment_id")]
        public long AssignmentId { get; set; }

        [ForeignKey(nameof(AssignmentId))]
        public virtual Assignment Assignment { get; set; } = null!;

        [Required]
        [Column("student_id")]
        public long StudentId { get; set; }

        [ForeignKey(nameof(StudentId))]
        public virtual User Student { get; set; } = null!;

        [Required]
        [Column("status")]
        public SubmissionStatus Status { get; set; } = SubmissionStatus.Draft;

        [Column("submitted_at")]
        public DateTime? SubmittedAt { get; set; }

        [Column("graded_at")]
        public DateTime? GradedAt { get; set; }

        [Column("graded_by")]
        public long? GradedBy { get; set; }

        [ForeignKey(nameof(GradedBy))]
        public virtual User? Grader { get; set; }

        [Column("content", TypeName = "TEXT")]
        public string? Content { get; set; }

        [StringLength(255)]
        [Column("file_url")]
        public string? FileUrl { get; set; }

        [StringLength(100)]
        [Column("file_name")]
        public string? FileName { get; set; }

        [Column("file_size_bytes")]
        public long? FileSizeBytes { get; set; }

        [StringLength(50)]
        [Column("file_type")]
        public string? FileType { get; set; }

        [Required]
        [Column("marks_obtained")]
        public double MarksObtained { get; set; } = 0;

        [StringLength(2000)]
        [Column("feedback")]
        public string? Feedback { get; set; }

        [Required]
        [Column("is_late")]
        public bool IsLate { get; set; } = false;

        [Column("late_penalty_applied")]
        public double? LatePenaltyApplied { get; set; }

        [Required]
        [Column("attempt_number")]
        public int AttemptNumber { get; set; } = 1;

        /// <summary>
        /// Get file size in human readable format
        /// </summary>
        [NotMapped]
        public string FileSizeFormatted
        {
            get
            {
                if (!FileSizeBytes.HasValue) return "Unknown";

                var size = FileSizeBytes.Value;
                string[] sizes = { "B", "KB", "MB", "GB" };
                int order = 0;
                while (size >= 1024 && order < sizes.Length - 1)
                {
                    order++;
                    size = size / 1024;
                }
                return $"{size:0.##} {sizes[order]}";
            }
        }

        /// <summary>
        /// Get score percentage
        /// </summary>
        public double ScorePercentage => Assignment?.MaxMarks > 0 ? (MarksObtained / Assignment.MaxMarks) * 100 : 0;

        /// <summary>
        /// Get grade based on score
        /// </summary>
        public string Grade
        {
            get
            {
                return ScorePercentage switch
                {
                    >= 90 => "A+",
                    >= 85 => "A",
                    >= 80 => "A-",
                    >= 75 => "B+",
                    >= 70 => "B",
                    >= 65 => "B-",
                    >= 60 => "C+",
                    >= 55 => "C",
                    >= 50 => "C-",
                    >= 45 => "D+",
                    >= 40 => "D",
                    _ => "F"
                };
            }
        }

        /// <summary>
        /// Check if submission is graded
        /// </summary>
        public bool IsGraded => Status == SubmissionStatus.Graded;

        /// <summary>
        /// Check if submission is submitted
        /// </summary>
        public bool IsSubmitted => Status != SubmissionStatus.Draft;

        /// <summary>
        /// Submit the assignment
        /// </summary>
        public void Submit()
        {
            if (Status == SubmissionStatus.Draft)
            {
                Status = SubmissionStatus.Submitted;
                SubmittedAt = DateTime.UtcNow;
                
                // Check if late
                if (Assignment?.DueDate.HasValue == true && SubmittedAt > Assignment.DueDate.Value)
                {
                    IsLate = true;
                    LatePenaltyApplied = Assignment.CalculateLatePenalty(SubmittedAt.Value);
                }
            }
        }

        /// <summary>
        /// Grade the submission
        /// </summary>
        public void GradeSubmission(double marks, string? feedback, long graderId)
        {
            MarksObtained = Math.Max(0, Math.Min(marks, Assignment?.MaxMarks ?? marks));
            
            // Apply late penalty if applicable
            if (IsLate && LatePenaltyApplied.HasValue)
            {
                MarksObtained = MarksObtained * (1 - LatePenaltyApplied.Value / 100);
            }

            Feedback = feedback;
            GradedBy = graderId;
            GradedAt = DateTime.UtcNow;
            Status = SubmissionStatus.Graded;
        }

        /// <summary>
        /// Return submission for revision
        /// </summary>
        public void ReturnForRevision(string feedback)
        {
            Status = SubmissionStatus.Returned;
            Feedback = feedback;
            GradedAt = DateTime.UtcNow;
        }
    }

    public enum SubmissionStatus
    {
        Draft,
        Submitted,
        UnderReview,
        Graded,
        Returned,
        Resubmitted
    }
}
