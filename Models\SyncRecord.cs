using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace LMSCBTSystem.Models
{
    /// <summary>
    /// Sync record entity for tracking data synchronization
    /// </summary>
    [Table("sync_records")]
    [Index(nameof(EntityType))]
    [Index(nameof(OperationType))]
    [Index(nameof(SyncStatus))]
    public class SyncRecord : BaseEntity
    {
        [Required]
        [StringLength(100)]
        [Column("entity_type")]
        public string EntityType { get; set; } = string.Empty;

        [Required]
        [Column("entity_id")]
        public long EntityId { get; set; }

        [Required]
        [Column("operation_type")]
        public OperationType OperationType { get; set; } = OperationType.Create;

        [Required]
        [Column("sync_status")]
        public SyncStatus SyncStatus { get; set; } = SyncStatus.Pending;

        // Aliases for compatibility
        public OperationType SyncType => OperationType;
        public SyncStatus Status => SyncStatus;

        [Column("sync_attempted_at")]
        public DateTime? SyncAttemptedAt { get; set; }

        [Column("sync_completed_at")]
        public DateTime? SyncCompletedAt { get; set; }

        [Required]
        [Column("retry_count")]
        public int RetryCount { get; set; } = 0;

        [Required]
        [Column("max_retries")]
        public int MaxRetries { get; set; } = 3;

        [StringLength(2000)]
        [Column("error_message")]
        public string? ErrorMessage { get; set; }

        [Column("data_snapshot", TypeName = "TEXT")]
        public string? DataSnapshot { get; set; }

        [StringLength(100)]
        [Column("sync_batch_id")]
        public string? SyncBatchId { get; set; }

        [Required]
        [Column("priority")]
        public int Priority { get; set; } = 5; // 1-10, 1 being highest priority

        [Column("conflict_resolution")]
        public ConflictResolution? ConflictResolution { get; set; }

        [StringLength(1000)]
        [Column("conflict_details")]
        public string? ConflictDetails { get; set; }

        [Column("local_version")]
        public long? LocalVersion { get; set; }

        [Column("remote_version")]
        public long? RemoteVersion { get; set; }

        /// <summary>
        /// Check if sync can be retried
        /// </summary>
        public bool CanRetry => RetryCount < MaxRetries && SyncStatus == SyncStatus.Failed;

        /// <summary>
        /// Check if sync is pending
        /// </summary>
        public bool IsPending => SyncStatus == SyncStatus.Pending;

        /// <summary>
        /// Check if sync is in progress
        /// </summary>
        public bool IsInProgress => SyncStatus == SyncStatus.InProgress;

        /// <summary>
        /// Check if sync is completed
        /// </summary>
        public bool IsCompleted => SyncStatus == SyncStatus.Completed;

        /// <summary>
        /// Check if sync has failed
        /// </summary>
        public bool IsFailed => SyncStatus == SyncStatus.Failed;

        /// <summary>
        /// Check if there's a conflict
        /// </summary>
        public bool HasConflict => SyncStatus == SyncStatus.Conflict;

        /// <summary>
        /// Mark sync as started
        /// </summary>
        public void MarkAsStarted()
        {
            SyncStatus = SyncStatus.InProgress;
            SyncAttemptedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Mark sync as completed
        /// </summary>
        public void MarkAsCompleted()
        {
            SyncStatus = SyncStatus.Completed;
            SyncCompletedAt = DateTime.UtcNow;
            ErrorMessage = null;
        }

        /// <summary>
        /// Mark sync as failed
        /// </summary>
        public void MarkAsFailed(string errorMessage)
        {
            SyncStatus = SyncStatus.Failed;
            ErrorMessage = errorMessage;
            RetryCount++;
        }

        /// <summary>
        /// Mark sync as having conflict
        /// </summary>
        public void MarkAsConflict(string conflictDetails)
        {
            SyncStatus = SyncStatus.Conflict;
            ConflictDetails = conflictDetails;
        }

        /// <summary>
        /// Reset for retry
        /// </summary>
        public void ResetForRetry()
        {
            if (CanRetry)
            {
                SyncStatus = SyncStatus.Pending;
                ErrorMessage = null;
                SyncAttemptedAt = null;
            }
        }

        /// <summary>
        /// Get next retry time
        /// </summary>
        public DateTime? GetNextRetryTime()
        {
            if (!CanRetry || !SyncAttemptedAt.HasValue) return null;

            // Exponential backoff: 1min, 2min, 4min, 8min, etc.
            var delayMinutes = Math.Pow(2, RetryCount - 1);
            return SyncAttemptedAt.Value.AddMinutes(delayMinutes);
        }

        /// <summary>
        /// Check if ready for retry
        /// </summary>
        public bool IsReadyForRetry()
        {
            if (!CanRetry) return false;
            var nextRetryTime = GetNextRetryTime();
            return nextRetryTime.HasValue && DateTime.UtcNow >= nextRetryTime.Value;
        }

        /// <summary>
        /// Get time until next retry
        /// </summary>
        public TimeSpan? TimeUntilNextRetry()
        {
            var nextRetryTime = GetNextRetryTime();
            if (!nextRetryTime.HasValue) return null;

            var timeUntil = nextRetryTime.Value - DateTime.UtcNow;
            return timeUntil.TotalSeconds > 0 ? timeUntil : TimeSpan.Zero;
        }
    }

    public enum OperationType
    {
        Create,
        Update,
        Delete,
        Sync
    }

    public enum SyncStatus
    {
        Pending,
        InProgress,
        Completed,
        Failed,
        Conflict,
        Cancelled
    }

    public enum ConflictResolution
    {
        UseLocal,
        UseRemote,
        Merge,
        Manual
    }
}
