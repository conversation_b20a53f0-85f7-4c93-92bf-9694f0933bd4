using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace LMSCBTSystem.Models
{
    /// <summary>
    /// Test entity representing CBT tests and exams
    /// Contains test configuration, timing, and settings
    /// </summary>
    [Table("tests")]
    [Index(nameof(CourseId))]
    [Index(nameof(Status))]
    [Index(nameof(StartTime))]
    public class Test : BaseEntity
    {
        [Required]
        [StringLength(200)]
        [Column("title")]
        public string Title { get; set; } = string.Empty;

        [StringLength(1000)]
        [Column("description")]
        public string? Description { get; set; }

        [Required]
        [Column("course_id")]
        public long CourseId { get; set; }

        [ForeignKey(nameof(CourseId))]
        public virtual Course Course { get; set; } = null!;

        [Required]
        [Column("test_type")]
        public TestType TestType { get; set; } = TestType.Quiz;

        [Required]
        [Column("status")]
        public TestStatus Status { get; set; } = TestStatus.Draft;

        [Column("start_time")]
        public DateTime? StartTime { get; set; }

        [Column("end_time")]
        public DateTime? EndTime { get; set; }

        [Required]
        [Column("duration_minutes")]
        public int DurationMinutes { get; set; } = 60;

        [Required]
        [Column("total_marks")]
        public double TotalMarks { get; set; } = 100;

        [Required]
        [Column("passing_marks")]
        public double PassingMarks { get; set; } = 50;

        [Required]
        [Column("max_attempts")]
        public int MaxAttempts { get; set; } = 1;

        [Required]
        [Column("shuffle_questions")]
        public bool ShuffleQuestions { get; set; } = true;

        [Required]
        [Column("shuffle_options")]
        public bool ShuffleOptions { get; set; } = true;

        [Required]
        [Column("show_results_immediately")]
        public bool ShowResultsImmediately { get; set; } = false;

        [Required]
        [Column("allow_review")]
        public bool AllowReview { get; set; } = true;

        [Required]
        [Column("require_webcam")]
        public bool RequireWebcam { get; set; } = false;

        [Required]
        [Column("require_fullscreen")]
        public bool RequireFullscreen { get; set; } = true;

        [Required]
        [Column("prevent_copy_paste")]
        public bool PreventCopyPaste { get; set; } = true;

        [Required]
        [Column("auto_submit")]
        public bool AutoSubmit { get; set; } = true;

        [StringLength(1000)]
        [Column("instructions")]
        public string? Instructions { get; set; }

        [Column("time_limit_warning_minutes")]
        public int? TimeLimitWarningMinutes { get; set; } = 5;

        [Required]
        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<Question> Questions { get; set; } = new List<Question>();
        public virtual ICollection<TestResult> TestResults { get; set; } = new List<TestResult>();

        /// <summary>
        /// Check if test is currently available for taking
        /// </summary>
        public bool IsAvailable
        {
            get
            {
                var now = DateTime.UtcNow;
                return IsActive && Status == TestStatus.Published &&
                       (!StartTime.HasValue || now >= StartTime.Value) &&
                       (!EndTime.HasValue || now <= EndTime.Value);
            }
        }

        /// <summary>
        /// Check if test has started
        /// </summary>
        public bool HasStarted => !StartTime.HasValue || DateTime.UtcNow >= StartTime.Value;

        /// <summary>
        /// Check if test has ended
        /// </summary>
        public bool HasEnded => EndTime.HasValue && DateTime.UtcNow > EndTime.Value;

        /// <summary>
        /// Get remaining time for the test
        /// </summary>
        public TimeSpan? RemainingTime
        {
            get
            {
                if (!EndTime.HasValue) return null;
                var remaining = EndTime.Value - DateTime.UtcNow;
                return remaining.TotalSeconds > 0 ? remaining : TimeSpan.Zero;
            }
        }

        /// <summary>
        /// Get total number of questions
        /// </summary>
        public int QuestionCount => Questions.Count;

        /// <summary>
        /// Calculate average marks per question
        /// </summary>
        public double AverageMarksPerQuestion => QuestionCount > 0 ? TotalMarks / QuestionCount : 0;

        /// <summary>
        /// Check if student can take the test
        /// </summary>
        public bool CanStudentTakeTest(long studentId)
        {
            if (!IsAvailable) return false;

            var studentAttempts = TestResults.Count(tr => tr.StudentId == studentId);
            return studentAttempts < MaxAttempts;
        }

        /// <summary>
        /// Get student's attempt count
        /// </summary>
        public int GetStudentAttemptCount(long studentId)
        {
            return TestResults.Count(tr => tr.StudentId == studentId);
        }

        /// <summary>
        /// Get student's best score
        /// </summary>
        public double? GetStudentBestScore(long studentId)
        {
            var results = TestResults.Where(tr => tr.StudentId == studentId && tr.Status == TestResultStatus.Completed);
            return results.Any() ? results.Max(tr => tr.ScorePercentage) : null;
        }

        /// <summary>
        /// Get test statistics
        /// </summary>
        public TestStatistics GetStatistics()
        {
            var completedResults = TestResults.Where(tr => tr.Status == TestResultStatus.Completed).ToList();
            
            return new TestStatistics
            {
                TotalAttempts = TestResults.Count,
                CompletedAttempts = completedResults.Count,
                AverageScore = completedResults.Any() ? completedResults.Average(tr => tr.ScorePercentage) : 0,
                HighestScore = completedResults.Any() ? completedResults.Max(tr => tr.ScorePercentage) : 0,
                LowestScore = completedResults.Any() ? completedResults.Min(tr => tr.ScorePercentage) : 0,
                PassRate = completedResults.Any() ? 
                    (double)completedResults.Count(tr => tr.ScorePercentage >= (PassingMarks / TotalMarks * 100)) / completedResults.Count * 100 : 0
            };
        }
    }

    public enum TestType
    {
        Quiz,
        Exam,
        Assignment,
        Practice,
        Survey
    }

    public enum TestStatus
    {
        Draft,
        Published,
        Active,
        Completed,
        Cancelled
    }

    public class TestStatistics
    {
        public int TotalAttempts { get; set; }
        public int CompletedAttempts { get; set; }
        public double AverageScore { get; set; }
        public double HighestScore { get; set; }
        public double LowestScore { get; set; }
        public double PassRate { get; set; }
    }
}
