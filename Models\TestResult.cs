using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace LMSCBTSystem.Models
{
    /// <summary>
    /// Test result entity for storing test attempt results
    /// </summary>
    [Table("test_results")]
    [Index(nameof(TestId))]
    [Index(nameof(StudentId))]
    [Index(nameof(Status))]
    public class TestResult : BaseEntity
    {
        [Required]
        [Column("test_id")]
        public long TestId { get; set; }

        [ForeignKey(nameof(TestId))]
        public virtual Test Test { get; set; } = null!;

        [Required]
        [Column("student_id")]
        public long StudentId { get; set; }

        [ForeignKey(nameof(StudentId))]
        public virtual User Student { get; set; } = null!;

        [Required]
        [Column("attempt_number")]
        public int AttemptNumber { get; set; } = 1;

        [Required]
        [Column("status")]
        public TestResultStatus Status { get; set; } = TestResultStatus.InProgress;

        [Column("started_at")]
        public DateTime? StartedAt { get; set; }

        [Column("submitted_at")]
        public DateTime? SubmittedAt { get; set; }

        [Column("completed_at")]
        public DateTime? CompletedAt { get; set; }

        [Required]
        [Column("total_questions")]
        public int TotalQuestions { get; set; }

        [Required]
        [Column("answered_questions")]
        public int AnsweredQuestions { get; set; } = 0;

        [Required]
        [Column("correct_answers")]
        public int CorrectAnswers { get; set; } = 0;

        [Required]
        [Column("total_marks")]
        public double TotalMarks { get; set; }

        [Required]
        [Column("marks_obtained")]
        public double MarksObtained { get; set; } = 0;

        [Required]
        [Column("score_percentage")]
        public double ScorePercentage { get; set; } = 0;

        [Required]
        [Column("time_taken_minutes")]
        public int TimeTakenMinutes { get; set; } = 0;

        [Required]
        [Column("is_passed")]
        public bool IsPassed { get; set; } = false;

        [StringLength(10)]
        [Column("grade")]
        public string? Grade { get; set; }

        [StringLength(45)]
        [Column("ip_address")]
        public string? IpAddress { get; set; }

        [StringLength(500)]
        [Column("user_agent")]
        public string? UserAgent { get; set; }

        [StringLength(1000)]
        [Column("browser_info")]
        public string? BrowserInfo { get; set; }

        [Required]
        [Column("proctoring_violations")]
        public int ProctoringViolations { get; set; } = 0;

        [StringLength(2000)]
        [Column("violation_details")]
        public string? ViolationDetails { get; set; }

        [Column("student_answers_json", TypeName = "TEXT")]
        public string? StudentAnswersJson { get; set; }

        // Navigation properties
        public virtual ICollection<Answer> Answers { get; set; } = new List<Answer>();

        /// <summary>
        /// Get student answers as dictionary
        /// </summary>
        [NotMapped]
        public Dictionary<long, string> StudentAnswers
        {
            get => string.IsNullOrEmpty(StudentAnswersJson) 
                ? new Dictionary<long, string>() 
                : JsonConvert.DeserializeObject<Dictionary<long, string>>(StudentAnswersJson) ?? new Dictionary<long, string>();
            set => StudentAnswersJson = JsonConvert.SerializeObject(value);
        }

        /// <summary>
        /// Get time taken as TimeSpan
        /// </summary>
        [NotMapped]
        public TimeSpan TimeTaken => TimeSpan.FromMinutes(TimeTakenMinutes);

        /// <summary>
        /// Get duration of the test attempt
        /// </summary>
        [NotMapped]
        public TimeSpan? Duration => StartedAt.HasValue && CompletedAt.HasValue 
            ? CompletedAt.Value - StartedAt.Value 
            : null;

        /// <summary>
        /// Check if test is currently in progress
        /// </summary>
        public bool IsInProgress => Status == TestResultStatus.InProgress;

        /// <summary>
        /// Check if test is completed
        /// </summary>
        public bool IsCompleted => Status == TestResultStatus.Completed;

        /// <summary>
        /// Get completion percentage
        /// </summary>
        public double CompletionPercentage => TotalQuestions > 0 ? (double)AnsweredQuestions / TotalQuestions * 100 : 0;

        /// <summary>
        /// Get accuracy percentage
        /// </summary>
        public double AccuracyPercentage => AnsweredQuestions > 0 ? (double)CorrectAnswers / AnsweredQuestions * 100 : 0;

        /// <summary>
        /// Calculate grade based on score
        /// </summary>
        public string CalculateGrade()
        {
            return ScorePercentage switch
            {
                >= 90 => "A+",
                >= 85 => "A",
                >= 80 => "A-",
                >= 75 => "B+",
                >= 70 => "B",
                >= 65 => "B-",
                >= 60 => "C+",
                >= 55 => "C",
                >= 50 => "C-",
                >= 45 => "D+",
                >= 40 => "D",
                _ => "F"
            };
        }

        /// <summary>
        /// Update score and grade
        /// </summary>
        public void UpdateScore()
        {
            if (TotalMarks > 0)
            {
                ScorePercentage = (MarksObtained / TotalMarks) * 100;
                IsPassed = Test != null && ScorePercentage >= (Test.PassingMarks / Test.TotalMarks * 100);
                Grade = CalculateGrade();
            }
        }

        /// <summary>
        /// Mark test as completed
        /// </summary>
        public void Complete()
        {
            Status = TestResultStatus.Completed;
            CompletedAt = DateTime.UtcNow;
            UpdateScore();
        }

        /// <summary>
        /// Add proctoring violation
        /// </summary>
        public void AddViolation(string violationDetail)
        {
            ProctoringViolations++;
            ViolationDetails = string.IsNullOrEmpty(ViolationDetails) 
                ? violationDetail 
                : $"{ViolationDetails}; {violationDetail}";
        }
    }

    public enum TestResultStatus
    {
        InProgress,
        Completed,
        Submitted,
        TimedOut,
        Cancelled,
        UnderReview
    }
}
