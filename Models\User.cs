using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace LMSCBTSystem.Models
{
    /// <summary>
    /// User entity representing all system users (students, teachers, administrators, staff)
    /// Contains comprehensive user information and authentication details
    /// </summary>
    [Table("users")]
    [Index(nameof(Email), IsUnique = true)]
    [Index(nameof(Username), IsUnique = true)]
    [Index(nameof(StudentId), IsUnique = true)]
    public class User : BaseEntity
    {
        [Required]
        [StringLength(50, MinimumLength = 3)]
        [Column("username")]
        public string Username { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        [StringLength(100)]
        [Column("email")]
        public string Email { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        [Column("password_hash")]
        public string PasswordHash { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        [Column("first_name")]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        [Column("last_name")]
        public string LastName { get; set; } = string.Empty;

        [StringLength(50)]
        [Column("middle_name")]
        public string? MiddleName { get; set; }

        [Column("date_of_birth")]
        public DateTime? DateOfBirth { get; set; }

        [Column("gender")]
        public Gender? Gender { get; set; }

        [StringLength(20)]
        [Column("phone_number")]
        public string? PhoneNumber { get; set; }

        [StringLength(500)]
        [Column("address")]
        public string? Address { get; set; }

        [StringLength(50)]
        [Column("student_id")]
        public string? StudentId { get; set; }

        [StringLength(50)]
        [Column("employee_id")]
        public string? EmployeeId { get; set; }

        [Required]
        [Column("role_id")]
        public long RoleId { get; set; }

        [ForeignKey(nameof(RoleId))]
        public virtual Role Role { get; set; } = null!;

        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        [Column("is_email_verified")]
        public bool IsEmailVerified { get; set; } = false;

        [Column("last_login")]
        public DateTime? LastLogin { get; set; }

        [Column("failed_login_attempts")]
        public int FailedLoginAttempts { get; set; } = 0;

        [Column("account_locked_until")]
        public DateTime? AccountLockedUntil { get; set; }

        [StringLength(255)]
        [Column("profile_picture_url")]
        public string? ProfilePictureUrl { get; set; }

        [StringLength(1000)]
        [Column("bio")]
        public string? Bio { get; set; }

        [Column("password_reset_token")]
        public string? PasswordResetToken { get; set; }

        [Column("password_reset_expires")]
        public DateTime? PasswordResetExpires { get; set; }

        [Column("email_verification_token")]
        public string? EmailVerificationToken { get; set; }

        [Column("email_verification_expires")]
        public DateTime? EmailVerificationExpires { get; set; }

        // Navigation properties
        public virtual ICollection<TestResult> TestResults { get; set; } = new List<TestResult>();
        public virtual ICollection<Course> TaughtCourses { get; set; } = new List<Course>();
        public virtual ICollection<Course> EnrolledCourses { get; set; } = new List<Course>();
        public virtual ICollection<Attendance> AttendanceRecords { get; set; } = new List<Attendance>();
        public virtual ICollection<Submission> Submissions { get; set; } = new List<Submission>();

        /// <summary>
        /// Get full name of the user
        /// </summary>
        public string FullName => $"{FirstName} {LastName}".Trim();

        /// <summary>
        /// Get display name with middle name if available
        /// </summary>
        public string DisplayName => string.IsNullOrEmpty(MiddleName) 
            ? FullName 
            : $"{FirstName} {MiddleName} {LastName}".Trim();

        /// <summary>
        /// Check if account is locked
        /// </summary>
        public bool IsAccountLocked => AccountLockedUntil.HasValue && AccountLockedUntil > DateTime.UtcNow;

        /// <summary>
        /// Check if password reset token is valid
        /// </summary>
        public bool IsPasswordResetTokenValid => !string.IsNullOrEmpty(PasswordResetToken) 
            && PasswordResetExpires.HasValue && PasswordResetExpires > DateTime.UtcNow;

        /// <summary>
        /// Check if email verification token is valid
        /// </summary>
        public bool IsEmailVerificationTokenValid => !string.IsNullOrEmpty(EmailVerificationToken) 
            && EmailVerificationExpires.HasValue && EmailVerificationExpires > DateTime.UtcNow;
    }

    public enum Gender
    {
        Male,
        Female,
        Other,
        PreferNotToSay
    }
}
