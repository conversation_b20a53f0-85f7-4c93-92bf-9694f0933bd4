# LMS CBT System - Desktop Application

A comprehensive Learning Management System with Computer-Based Testing capabilities built with JavaFX.

## 🚀 Quick Start

### Prerequisites
- **Java 17 or higher** (with JavaFX support)
- **Windows 10/11** (current build is Windows-specific)

### Running the Application

1. **Download the pre-built JAR** (if available) or build from source
2. **Double-click** `run-app.bat` to start the application
3. **Login** with default admin credentials:
   - Username: `admin`
   - Password: `Admin@123`

## 🔧 Building from Source

### Option 1: Quick Build (Recommended)
```bash
# Run the build script
build-jar.bat

# Run the application
run-app.bat
```

### Option 2: Manual Build
```bash
# Clean and build
mvn clean package -DskipTests

# Run the application
java --add-modules javafx.controls,javafx.fxml,javafx.web,javafx.media ^
     --add-exports javafx.graphics/com.sun.javafx.application=ALL-UNNAMED ^
     --add-exports javafx.base/com.sun.javafx.runtime=ALL-UNNAMED ^
     -Djava.awt.headless=false ^
     -Dfile.encoding=UTF-8 ^
     -jar target\lms-cbt-system-1.0.0-jar-with-dependencies.jar
```

## 📁 Project Structure

```
lmssoft/
├── src/main/java/com/lmssoft/
│   ├── config/          # Configuration management
│   ├── controller/      # JavaFX controllers
│   ├── model/          # JPA entity models
│   ├── service/        # Business logic services
│   ├── util/           # Utility classes
│   └── LMSApplication.java
├── src/main/resources/
│   ├── css/            # Stylesheets
│   ├── fxml/           # JavaFX FXML files
│   ├── application.yml # Configuration file
│   └── logback.xml     # Logging configuration
├── build-jar.bat      # Build script
├── run-app.bat        # Run script
└── pom.xml            # Maven configuration
```

## ✨ Features

### 🎨 Modern UI
- **Material Design** inspired interface
- **Smooth animations** and transitions
- **Responsive design** with modern styling
- **Role-based dashboards** for different user types

### 🔐 Authentication & Security
- **Secure login** with BCrypt password hashing
- **JWT token** based session management
- **Role-Based Access Control** (RBAC)
- **Account security** features (login attempts, etc.)

### 👥 User Roles
- **Administrator** - Full system access and management
- **Teacher** - Course and test management
- **Student** - Course access and test taking
- **Staff** - Attendance and basic management

### 💾 Database Support
- **Offline Mode** - SQLite for local storage
- **Online Mode** - MySQL for server deployment
- **Data Synchronization** - Automatic sync between offline/online
- **Hibernate ORM** - Robust data persistence

### 📊 Dashboard Features
- **Student Dashboard** - Course overview, test tracking, grades
- **Admin Dashboard** - System overview, user management, analytics
- **Real-time Status** - System health and connection monitoring
- **Quick Actions** - Easy access to common tasks

## 🗄️ Database Schema

The system includes comprehensive entities:
- **Users** - Authentication and profile management
- **Roles** - Permission-based access control
- **Courses** - Course management and enrollment
- **Tests** - CBT test creation and management
- **Questions** - Multiple question types support
- **Results** - Test results and analytics
- **Attendance** - Attendance tracking
- **Assignments** - Assignment management
- **Grades** - Grade calculation and reporting

## 🔧 Configuration

### Application Settings (`src/main/resources/application.yml`)
```yaml
app:
  title: "LMS CBT System"
  version: "1.0.0"
  
database:
  offline:
    url: "jdbc:sqlite:lms_cbt.db"
  online:
    url: "***********************************"
    username: "lms_user"
    password: "lms_password"

security:
  jwt:
    secret: "your-secret-key"
    expiration: 86400000  # 24 hours
```

## 🚨 Troubleshooting

### Common Issues

1. **Application won't start**
   - Ensure Java 17+ is installed: `java --version`
   - Check if JavaFX is available in your Java installation
   - Try running with verbose output: `java -jar target\lms-cbt-system-1.0.0-jar-with-dependencies.jar`

2. **Module warnings**
   - These are normal and can be safely ignored
   - The application uses automatic modules for some dependencies

3. **Database connection issues**
   - The application will automatically create a local SQLite database
   - Check the logs in the console for specific error messages

4. **Login issues**
   - Use default credentials: admin/Admin@123
   - The system automatically creates default roles and admin user on first run

### Getting Help

If you encounter issues:
1. Check the console output for error messages
2. Review the log files (if configured)
3. Ensure all prerequisites are met
4. Try rebuilding the application: `build-jar.bat`

## 🔮 Future Development

The current implementation provides a solid foundation with:
- ✅ Authentication and user management
- ✅ Modern UI framework and navigation
- ✅ Database design and models
- ✅ Basic dashboard functionality

**Planned features:**
- 🔄 Complete CBT test creation and taking interface
- 📚 Full course management system
- 📊 Advanced analytics and reporting
- 📱 Mobile-responsive design
- 🌐 Web-based version
- 🔄 Enhanced offline-online synchronization

## 📄 License

This project is developed for educational purposes. All rights reserved.

---

**LMS CBT System v1.0.0** - A modern, secure, and scalable learning management solution.
