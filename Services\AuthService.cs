using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

using LMSCBTSystem.Models;
using System.Security.Cryptography;
using System.Text;

namespace LMSCBTSystem.Services
{
    /// <summary>
    /// Authentication service implementation
    /// </summary>
    public class AuthService : IAuthService
    {
        private readonly LMSDbContext _context;
        private readonly IConfiguration _config;
        private readonly ILogger<AuthService> _logger;
        private User? _currentUser;

        public AuthService(LMSDbContext context, IConfiguration config, ILogger<AuthService> logger)
        {
            _context = context;
            _config = config;
            _logger = logger;
        }

        public bool IsAuthenticated => _currentUser != null;
        public long? CurrentUserId => _currentUser?.Id;

        public async Task<AuthResult> AuthenticateAsync(string usernameOrEmail, string password)
        {
            try
            {
                _logger.LogInformation("Attempting authentication for: {UsernameOrEmail}", usernameOrEmail);

                if (string.IsNullOrWhiteSpace(usernameOrEmail) || string.IsNullOrWhiteSpace(password))
                {
                    return AuthResult.Failed("Username/email and password are required");
                }

                // Find user by username or email
                var user = await _context.Users
                    .Include(u => u.Role)
                    .ThenInclude(r => r.RolePermissions)
                    .FirstOrDefaultAsync(u => 
                        (u.Username == usernameOrEmail || u.Email == usernameOrEmail) && 
                        !u.IsDeleted);

                if (user == null)
                {
                    await RecordFailedLoginAsync(usernameOrEmail);
                    return AuthResult.Failed("Invalid username/email or password");
                }

                // Check if account is locked
                if (user.IsAccountLocked)
                {
                    _logger.LogWarning("Login attempt for locked account: {UserId}", user.Id);
                    return AuthResult.Failed($"Account is locked until {user.AccountLockedUntil:yyyy-MM-dd HH:mm:ss}");
                }

                // Check if account is active
                if (!user.IsActive)
                {
                    return AuthResult.Failed("Account is disabled");
                }

                // Verify password
                if (!BCrypt.Net.BCrypt.Verify(password, user.PasswordHash))
                {
                    await RecordFailedLoginAsync(usernameOrEmail);
                    return AuthResult.Failed("Invalid username/email or password");
                }

                // Reset failed login attempts on successful login
                await ResetFailedLoginAttemptsAsync(user.Id);

                // Update last login
                user.LastLogin = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                _logger.LogInformation("User authenticated successfully: {UserId}", user.Id);
                return AuthResult.Successful(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during authentication for: {UsernameOrEmail}", usernameOrEmail);
                return AuthResult.Failed("An error occurred during authentication");
            }
        }

        public async Task<AuthResult> RegisterAsync(User user, string password)
        {
            try
            {
                _logger.LogInformation("Attempting to register user: {Username}", user.Username);

                // Validate password
                var passwordValidation = ValidatePassword(password);
                if (!passwordValidation.IsValid)
                {
                    return AuthResult.Failed($"Password validation failed: {string.Join(", ", passwordValidation.Errors)}");
                }

                // Check if username already exists
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == user.Username && !u.IsDeleted);
                if (existingUser != null)
                {
                    return AuthResult.Failed("Username already exists");
                }

                // Check if email already exists
                existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Email == user.Email && !u.IsDeleted);
                if (existingUser != null)
                {
                    return AuthResult.Failed("Email already exists");
                }

                // Hash password
                user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(password);

                // Set default role if not specified
                if (user.RoleId == 0)
                {
                    var studentRole = await _context.Roles.FirstOrDefaultAsync(r => r.Name == "Student");
                    if (studentRole != null)
                    {
                        user.RoleId = studentRole.Id;
                    }
                }

                // Add user to database
                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                _logger.LogInformation("User registered successfully: {UserId}", user.Id);
                return AuthResult.Successful(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during user registration: {Username}", user.Username);
                return AuthResult.Failed("An error occurred during registration");
            }
        }

        public async Task<bool> ChangePasswordAsync(long userId, string currentPassword, string newPassword)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null || user.IsDeleted)
                {
                    return false;
                }

                // Verify current password
                if (!BCrypt.Net.BCrypt.Verify(currentPassword, user.PasswordHash))
                {
                    return false;
                }

                // Validate new password
                var validation = ValidatePassword(newPassword);
                if (!validation.IsValid)
                {
                    return false;
                }

                // Update password
                user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Password changed successfully for user: {UserId}", userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing password for user: {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> ResetPasswordAsync(string token, string newPassword)
        {
            try
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.PasswordResetToken == token && !u.IsDeleted);

                if (user == null || !user.IsPasswordResetTokenValid)
                {
                    return false;
                }

                // Validate new password
                var validation = ValidatePassword(newPassword);
                if (!validation.IsValid)
                {
                    return false;
                }

                // Update password and clear reset token
                user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
                user.PasswordResetToken = null;
                user.PasswordResetExpires = null;
                await _context.SaveChangesAsync();

                _logger.LogInformation("Password reset successfully for user: {UserId}", user.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting password with token: {Token}", token);
                return false;
            }
        }

        public async Task<string?> GeneratePasswordResetTokenAsync(string email)
        {
            try
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Email == email && !u.IsDeleted);

                if (user == null)
                {
                    return null;
                }

                // Generate secure token
                var token = GenerateSecureToken();
                user.PasswordResetToken = token;
                user.PasswordResetExpires = DateTime.UtcNow.AddHours(24); // Token expires in 24 hours

                await _context.SaveChangesAsync();

                _logger.LogInformation("Password reset token generated for user: {UserId}", user.Id);
                return token;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating password reset token for email: {Email}", email);
                return null;
            }
        }

        public async Task<bool> VerifyEmailAsync(string token)
        {
            try
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.EmailVerificationToken == token && !u.IsDeleted);

                if (user == null || !user.IsEmailVerificationTokenValid)
                {
                    return false;
                }

                user.IsEmailVerified = true;
                user.EmailVerificationToken = null;
                user.EmailVerificationExpires = null;
                await _context.SaveChangesAsync();

                _logger.LogInformation("Email verified successfully for user: {UserId}", user.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying email with token: {Token}", token);
                return false;
            }
        }

        public async Task<string?> GenerateEmailVerificationTokenAsync(long userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null || user.IsDeleted)
                {
                    return null;
                }

                var token = GenerateSecureToken();
                user.EmailVerificationToken = token;
                user.EmailVerificationExpires = DateTime.UtcNow.AddDays(7); // Token expires in 7 days

                await _context.SaveChangesAsync();

                _logger.LogInformation("Email verification token generated for user: {UserId}", userId);
                return token;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating email verification token for user: {UserId}", userId);
                return null;
            }
        }

        public ValidationResult ValidatePassword(string password)
        {
            var result = new ValidationResult { IsValid = true };

            if (string.IsNullOrWhiteSpace(password))
            {
                result.AddError("Password is required");
                return result;
            }

            var minLength = _config.GetValue<int>("Security:Password:MinLength", 8);
            var requireSpecialChars = _config.GetValue<bool>("Security:Password:RequireSpecialChars", true);
            var requireNumbers = _config.GetValue<bool>("Security:Password:RequireNumbers", true);
            var requireUppercase = _config.GetValue<bool>("Security:Password:RequireUppercase", true);
            var requireLowercase = _config.GetValue<bool>("Security:Password:RequireLowercase", true);

            if (password.Length < minLength)
            {
                result.AddError($"Password must be at least {minLength} characters long");
            }

            if (requireUppercase && !password.Any(char.IsUpper))
            {
                result.AddError("Password must contain at least one uppercase letter");
            }

            if (requireLowercase && !password.Any(char.IsLower))
            {
                result.AddError("Password must contain at least one lowercase letter");
            }

            if (requireNumbers && !password.Any(char.IsDigit))
            {
                result.AddError("Password must contain at least one number");
            }

            if (requireSpecialChars && !password.Any(c => !char.IsLetterOrDigit(c)))
            {
                result.AddError("Password must contain at least one special character");
            }

            return result;
        }

        public async Task<bool> HasPermissionAsync(long userId, string permission)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.Role)
                    .ThenInclude(r => r.RolePermissions)
                    .FirstOrDefaultAsync(u => u.Id == userId && !u.IsDeleted);

                return user?.Role?.HasPermission(permission) == true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking permission {Permission} for user: {UserId}", permission, userId);
                return false;
            }
        }

        public async Task<User?> GetCurrentUserAsync()
        {
            if (_currentUser != null)
            {
                // Refresh user data from database
                _currentUser = await _context.Users
                    .Include(u => u.Role)
                    .ThenInclude(r => r.RolePermissions)
                    .FirstOrDefaultAsync(u => u.Id == _currentUser.Id && !u.IsDeleted);
            }

            return _currentUser;
        }

        public void SetCurrentUser(User user)
        {
            _currentUser = user;
            _logger.LogInformation("Current user set: {UserId}", user.Id);
        }

        public async Task<AuthResult> LoginAsync(string usernameOrEmail, string password)
        {
            return await AuthenticateAsync(usernameOrEmail, password);
        }

        public void Logout()
        {
            if (_currentUser != null)
            {
                _logger.LogInformation("User logged out: {UserId}", _currentUser.Id);
                _currentUser = null;
            }
        }

        public async Task LogoutAsync()
        {
            await Task.Run(() => Logout());
        }

        public async Task LockAccountAsync(long userId, TimeSpan lockDuration)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user != null && !user.IsDeleted)
                {
                    user.AccountLockedUntil = DateTime.UtcNow.Add(lockDuration);
                    await _context.SaveChangesAsync();

                    _logger.LogWarning("Account locked for user: {UserId} until {LockedUntil}", userId, user.AccountLockedUntil);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error locking account for user: {UserId}", userId);
            }
        }

        public async Task UnlockAccountAsync(long userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user != null && !user.IsDeleted)
                {
                    user.AccountLockedUntil = null;
                    user.FailedLoginAttempts = 0;
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Account unlocked for user: {UserId}", userId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unlocking account for user: {UserId}", userId);
            }
        }

        public async Task RecordFailedLoginAsync(string usernameOrEmail)
        {
            try
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => 
                        (u.Username == usernameOrEmail || u.Email == usernameOrEmail) && 
                        !u.IsDeleted);

                if (user != null)
                {
                    user.FailedLoginAttempts++;

                    // Lock account after 5 failed attempts
                    if (user.FailedLoginAttempts >= 5)
                    {
                        user.AccountLockedUntil = DateTime.UtcNow.AddMinutes(30); // Lock for 30 minutes
                        _logger.LogWarning("Account locked due to failed login attempts: {UserId}", user.Id);
                    }

                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recording failed login for: {UsernameOrEmail}", usernameOrEmail);
            }
        }

        public async Task ResetFailedLoginAttemptsAsync(long userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user != null && !user.IsDeleted)
                {
                    user.FailedLoginAttempts = 0;
                    user.AccountLockedUntil = null;
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting failed login attempts for user: {UserId}", userId);
            }
        }

        private static string GenerateSecureToken()
        {
            using var rng = RandomNumberGenerator.Create();
            var bytes = new byte[32];
            rng.GetBytes(bytes);
            return Convert.ToBase64String(bytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
        }
    }
}
