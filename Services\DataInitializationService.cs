using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using LMSCBTSystem.Models;

namespace LMSCBTSystem.Services
{
    /// <summary>
    /// Data initialization service implementation
    /// </summary>
    public class DataInitializationService : IDataInitializationService
    {
        private readonly LMSDbContext _context;
        private readonly ILogger<DataInitializationService> _logger;

        public DataInitializationService(LMSDbContext context, ILogger<DataInitializationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<bool> IsSystemInitializedAsync()
        {
            try
            {
                // Check if admin user exists
                var adminExists = await _context.Users
                    .AnyAsync(u => u.Username == "admin" && !u.IsDeleted);

                // Check if roles exist
                var rolesExist = await _context.Roles
                    .AnyAsync(r => r.Name == "Administrator");

                return adminExists && rolesExist;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if system is initialized");
                return false;
            }
        }

        public async Task InitializeDefaultDataAsync()
        {
            try
            {
                _logger.LogInformation("Initializing default system data...");

                // Create default roles if they don't exist
                await CreateDefaultRolesAsync();

                // Create default admin user if it doesn't exist
                await CreateDefaultAdminUserAsync();

                _logger.LogInformation("Default system data initialization completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing default data");
                throw;
            }
        }

        public async Task CreateDefaultAdminUserAsync()
        {
            try
            {
                var adminExists = await _context.Users
                    .AnyAsync(u => u.Username == "admin" && !u.IsDeleted);

                if (!adminExists)
                {
                    _logger.LogInformation("Creating default admin user...");

                    var adminRole = await _context.Roles
                        .FirstOrDefaultAsync(r => r.Name == "Administrator");

                    if (adminRole == null)
                    {
                        _logger.LogError("Administrator role not found. Cannot create admin user.");
                        return;
                    }

                    var adminUser = new User
                    {
                        Username = "admin",
                        Email = "<EMAIL>",
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword("Admin@123"),
                        FirstName = "System",
                        LastName = "Administrator",
                        RoleId = adminRole.Id,
                        IsActive = true,
                        IsEmailVerified = true
                    };

                    _context.Users.Add(adminUser);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Default admin user created successfully");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating default admin user");
                throw;
            }
        }

        public async Task CreateDefaultRolesAsync()
        {
            try
            {
                _logger.LogInformation("Creating default roles...");

                var existingRoles = await _context.Roles
                    .Select(r => r.Name)
                    .ToListAsync();

                var defaultRoles = new[]
                {
                    new { Name = "Administrator", Description = "System Administrator with full access" },
                    new { Name = "Teacher", Description = "Course Teacher with teaching privileges" },
                    new { Name = "Student", Description = "Student with learning access" },
                    new { Name = "Staff", Description = "Administrative Staff with limited access" }
                };

                foreach (var roleInfo in defaultRoles)
                {
                    if (!existingRoles.Contains(roleInfo.Name))
                    {
                        var role = new Role
                        {
                            Name = roleInfo.Name,
                            Description = roleInfo.Description,
                            IsActive = true
                        };

                        _context.Roles.Add(role);
                        _logger.LogInformation("Created role: {RoleName}", roleInfo.Name);
                    }
                }

                await _context.SaveChangesAsync();

                // Create default permissions for roles
                await CreateDefaultPermissionsAsync();

                _logger.LogInformation("Default roles creation completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating default roles");
                throw;
            }
        }

        public async Task CreateSampleDataAsync()
        {
            try
            {
                _logger.LogInformation("Creating sample data...");

                // Create sample teacher
                await CreateSampleTeacherAsync();

                // Create sample students
                await CreateSampleStudentsAsync();

                // Create sample course
                await CreateSampleCourseAsync();

                _logger.LogInformation("Sample data creation completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating sample data");
                throw;
            }
        }

        public async Task ResetToDefaultAsync()
        {
            try
            {
                _logger.LogInformation("Resetting system to default state...");

                // Clear all data except roles
                _context.TestResults.RemoveRange(_context.TestResults);
                _context.Answers.RemoveRange(_context.Answers);
                _context.Questions.RemoveRange(_context.Questions);
                _context.Tests.RemoveRange(_context.Tests);
                _context.Submissions.RemoveRange(_context.Submissions);
                _context.Assignments.RemoveRange(_context.Assignments);
                _context.Attendances.RemoveRange(_context.Attendances);
                _context.Grades.RemoveRange(_context.Grades);
                _context.Resources.RemoveRange(_context.Resources);
                _context.Notifications.RemoveRange(_context.Notifications);
                _context.SyncRecords.RemoveRange(_context.SyncRecords);
                _context.Courses.RemoveRange(_context.Courses);
                _context.Users.RemoveRange(_context.Users);

                await _context.SaveChangesAsync();

                // Recreate default data
                await InitializeDefaultDataAsync();

                _logger.LogInformation("System reset to default state completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting system to default");
                throw;
            }
        }

        public async Task<List<string>> ValidateSystemIntegrityAsync()
        {
            var issues = new List<string>();

            try
            {
                _logger.LogInformation("Validating system integrity...");

                // Check if admin user exists
                var adminExists = await _context.Users
                    .AnyAsync(u => u.Username == "admin" && !u.IsDeleted);
                if (!adminExists)
                {
                    issues.Add("Default admin user not found");
                }

                // Check if all required roles exist
                var requiredRoles = new[] { "Administrator", "Teacher", "Student", "Staff" };
                var existingRoles = await _context.Roles
                    .Where(r => requiredRoles.Contains(r.Name))
                    .Select(r => r.Name)
                    .ToListAsync();

                foreach (var requiredRole in requiredRoles)
                {
                    if (!existingRoles.Contains(requiredRole))
                    {
                        issues.Add($"Required role '{requiredRole}' not found");
                    }
                }

                // Check for orphaned records
                var orphanedUsers = await _context.Users
                    .Where(u => !_context.Roles.Any(r => r.Id == u.RoleId))
                    .CountAsync();
                if (orphanedUsers > 0)
                {
                    issues.Add($"{orphanedUsers} users have invalid role references");
                }

                var orphanedCourses = await _context.Courses
                    .Where(c => !_context.Users.Any(u => u.Id == c.TeacherId))
                    .CountAsync();
                if (orphanedCourses > 0)
                {
                    issues.Add($"{orphanedCourses} courses have invalid teacher references");
                }

                _logger.LogInformation("System integrity validation completed. Found {IssueCount} issues", issues.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating system integrity");
                issues.Add($"Error during validation: {ex.Message}");
            }

            return issues;
        }

        private async Task CreateDefaultPermissionsAsync()
        {
            try
            {
                var roles = await _context.Roles.ToListAsync();

                foreach (var role in roles)
                {
                    // Clear existing permissions
                    var existingPermissions = await _context.RolePermissions
                        .Where(rp => rp.RoleId == role.Id)
                        .ToListAsync();
                    _context.RolePermissions.RemoveRange(existingPermissions);

                    // Add permissions based on role
                    var permissions = GetPermissionsForRole(role.Name);
                    foreach (var permission in permissions)
                    {
                        _context.RolePermissions.Add(new RolePermission
                        {
                            RoleId = role.Id,
                            Permission = permission
                        });
                    }
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating default permissions");
                throw;
            }
        }

        private static List<string> GetPermissionsForRole(string roleName)
        {
            return roleName switch
            {
                "Administrator" => new List<string>
                {
                    Permissions.SYSTEM_ADMIN,
                    Permissions.USER_CREATE, Permissions.USER_READ, Permissions.USER_UPDATE, Permissions.USER_DELETE,
                    Permissions.COURSE_CREATE, Permissions.COURSE_READ, Permissions.COURSE_UPDATE, Permissions.COURSE_DELETE,
                    Permissions.TEST_CREATE, Permissions.TEST_READ, Permissions.TEST_UPDATE, Permissions.TEST_DELETE, Permissions.TEST_GRADE,
                    Permissions.SYSTEM_REPORTS, Permissions.DATA_EXPORT, Permissions.DATA_IMPORT
                },
                "Teacher" => new List<string>
                {
                    Permissions.USER_READ,
                    Permissions.COURSE_READ, Permissions.COURSE_UPDATE,
                    Permissions.TEST_CREATE, Permissions.TEST_READ, Permissions.TEST_UPDATE, Permissions.TEST_GRADE
                },
                "Student" => new List<string>
                {
                    Permissions.COURSE_READ, Permissions.COURSE_ENROLL,
                    Permissions.TEST_READ, Permissions.TEST_TAKE
                },
                "Staff" => new List<string>
                {
                    Permissions.USER_READ,
                    Permissions.COURSE_READ,
                    Permissions.TEST_READ
                },
                _ => new List<string>()
            };
        }

        private async Task CreateSampleTeacherAsync()
        {
            var teacherExists = await _context.Users
                .AnyAsync(u => u.Username == "teacher1" && !u.IsDeleted);

            if (!teacherExists)
            {
                var teacherRole = await _context.Roles
                    .FirstOrDefaultAsync(r => r.Name == "Teacher");

                if (teacherRole != null)
                {
                    var teacher = new User
                    {
                        Username = "teacher1",
                        Email = "<EMAIL>",
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword("Teacher@123"),
                        FirstName = "John",
                        LastName = "Smith",
                        RoleId = teacherRole.Id,
                        IsActive = true,
                        IsEmailVerified = true,
                        EmployeeId = "EMP001"
                    };

                    _context.Users.Add(teacher);
                    await _context.SaveChangesAsync();
                }
            }
        }

        private async Task CreateSampleStudentsAsync()
        {
            var studentRole = await _context.Roles
                .FirstOrDefaultAsync(r => r.Name == "Student");

            if (studentRole == null) return;

            var sampleStudents = new[]
            {
                new { Username = "student1", Email = "<EMAIL>", FirstName = "Alice", LastName = "Johnson", StudentId = "STU001" },
                new { Username = "student2", Email = "<EMAIL>", FirstName = "Bob", LastName = "Wilson", StudentId = "STU002" }
            };

            foreach (var studentInfo in sampleStudents)
            {
                var exists = await _context.Users
                    .AnyAsync(u => u.Username == studentInfo.Username && !u.IsDeleted);

                if (!exists)
                {
                    var student = new User
                    {
                        Username = studentInfo.Username,
                        Email = studentInfo.Email,
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword("Student@123"),
                        FirstName = studentInfo.FirstName,
                        LastName = studentInfo.LastName,
                        RoleId = studentRole.Id,
                        IsActive = true,
                        IsEmailVerified = true,
                        StudentId = studentInfo.StudentId
                    };

                    _context.Users.Add(student);
                }
            }

            await _context.SaveChangesAsync();
        }

        private async Task CreateSampleCourseAsync()
        {
            var courseExists = await _context.Courses
                .AnyAsync(c => c.CourseCode == "CS101");

            if (!courseExists)
            {
                var teacher = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == "teacher1");

                if (teacher != null)
                {
                    var course = new Course
                    {
                        CourseCode = "CS101",
                        Name = "Introduction to Computer Science",
                        Description = "Basic concepts of computer science and programming",
                        TeacherId = teacher.Id,
                        Credits = 3,
                        StartDate = DateTime.Now.Date,
                        EndDate = DateTime.Now.Date.AddMonths(4),
                        Status = CourseStatus.Active,
                        MaxEnrollment = 30,
                        Semester = "Fall 2024",
                        AcademicYear = "2024",
                        Department = "Computer Science",
                        AllowSelfEnrollment = true
                    };

                    _context.Courses.Add(course);
                    await _context.SaveChangesAsync();
                }
            }
        }
    }
}
