using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using LMSCBTSystem.Configuration;
using System.Diagnostics;

namespace LMSCBTSystem.Services
{
    /// <summary>
    /// Database service implementation
    /// </summary>
    public class DatabaseService : IDatabaseService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<DatabaseService> _logger;
        private bool _isInitialized = false;

        public DatabaseService(IServiceProvider serviceProvider, ILogger<DatabaseService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        public bool IsInitialized => _isInitialized;
        public bool IsOnline => true; // Always true for SQLite

        public async Task InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Initializing database service...");

                await DatabaseConfiguration.InitializeDatabaseAsync(_serviceProvider);
                _isInitialized = true;

                _logger.LogInformation("Database service initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize database service");
                throw;
            }
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                return await DatabaseConfiguration.TestConnectionAsync(_serviceProvider);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing database connection");
                return false;
            }
        }

        public async Task<DatabaseStatistics> GetStatisticsAsync()
        {
            try
            {
                return await DatabaseConfiguration.GetDatabaseStatisticsAsync(_serviceProvider);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting database statistics");
                return new DatabaseStatistics();
            }
        }

        public async Task<bool> BackupAsync(string backupPath)
        {
            try
            {
                _logger.LogInformation("Creating database backup: {BackupPath}", backupPath);
                return await DatabaseConfiguration.BackupDatabaseAsync(_serviceProvider, backupPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating database backup");
                return false;
            }
        }

        public async Task<bool> RestoreAsync(string backupPath)
        {
            try
            {
                _logger.LogInformation("Restoring database from backup: {BackupPath}", backupPath);
                return await DatabaseConfiguration.RestoreDatabaseAsync(_serviceProvider, backupPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error restoring database from backup");
                return false;
            }
        }

        public async Task<DatabaseHealthStatus> GetHealthStatusAsync()
        {
            var stopwatch = Stopwatch.StartNew();
            var status = new DatabaseHealthStatus
            {
                LastChecked = DateTime.UtcNow
            };

            try
            {
                // Test connection
                var isConnected = await TestConnectionAsync();
                stopwatch.Stop();

                status.ResponseTime = stopwatch.Elapsed;
                status.IsHealthy = isConnected;
                status.Status = isConnected ? "Healthy" : "Unhealthy";

                if (!isConnected)
                {
                    status.Issues.Add("Database connection failed");
                }

                // Get database size (for SQLite)
                try
                {
                    status.DatabaseSize = await GetDatabaseSizeAsync();
                }
                catch (Exception ex)
                {
                    status.Issues.Add($"Could not determine database size: {ex.Message}");
                }

                // Check for other potential issues
                await CheckForIssuesAsync(status);

                _logger.LogInformation("Database health check completed: {Status}", status.Status);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                status.ResponseTime = stopwatch.Elapsed;
                status.IsHealthy = false;
                status.Status = "Error";
                status.Issues.Add($"Health check failed: {ex.Message}");

                _logger.LogError(ex, "Error during database health check");
            }

            return status;
        }

        public async Task OptimizeDatabaseAsync()
        {
            try
            {
                _logger.LogInformation("Optimizing database...");

                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<Data.LMSDbContext>();

                // For SQLite, run VACUUM to optimize
                await context.Database.ExecuteSqlRawAsync("VACUUM");
                await context.Database.ExecuteSqlRawAsync("ANALYZE");

                _logger.LogInformation("Database optimization completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error optimizing database");
                throw;
            }
        }

        public async Task CleanupOldDataAsync(TimeSpan retentionPeriod)
        {
            try
            {
                _logger.LogInformation("Cleaning up old data older than {RetentionPeriod}", retentionPeriod);

                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<Data.LMSDbContext>();

                var cutoffDate = DateTime.UtcNow.Subtract(retentionPeriod);

                // Clean up old notifications
                var oldNotifications = context.Notifications
                    .Where(n => n.CreatedAt < cutoffDate && n.IsRead);
                context.Notifications.RemoveRange(oldNotifications);

                // Clean up old sync records
                var oldSyncRecords = context.SyncRecords
                    .Where(sr => sr.CreatedAt < cutoffDate && sr.SyncStatus == Models.SyncStatus.Completed);
                context.SyncRecords.RemoveRange(oldSyncRecords);

                // Clean up soft-deleted records
                var oldDeletedUsers = context.Users
                    .Where(u => u.IsDeleted && u.UpdatedAt < cutoffDate);
                context.Users.RemoveRange(oldDeletedUsers);

                var deletedCount = await context.SaveChangesAsync();
                _logger.LogInformation("Cleaned up {DeletedCount} old records", deletedCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up old data");
                throw;
            }
        }

        public async Task<bool> ExportDataAsync(string filePath, ExportFormat format)
        {
            try
            {
                _logger.LogInformation("Exporting data to {FilePath} in {Format} format", filePath, format);

                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<Data.LMSDbContext>();

                switch (format)
                {
                    case ExportFormat.Json:
                        return await ExportToJsonAsync(context, filePath);
                    case ExportFormat.Csv:
                        return await ExportToCsvAsync(context, filePath);
                    case ExportFormat.Sql:
                        return await ExportToSqlAsync(context, filePath);
                    default:
                        _logger.LogWarning("Unsupported export format: {Format}", format);
                        return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting data");
                return false;
            }
        }

        public async Task<bool> ImportDataAsync(string filePath, ImportOptions options)
        {
            try
            {
                _logger.LogInformation("Importing data from {FilePath}", filePath);

                if (!File.Exists(filePath))
                {
                    _logger.LogError("Import file not found: {FilePath}", filePath);
                    return false;
                }

                // Create backup if requested
                if (options.CreateBackup)
                {
                    var backupPath = $"backup_{DateTime.UtcNow:yyyyMMdd_HHmmss}.db";
                    await BackupAsync(backupPath);
                }

                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<Data.LMSDbContext>();

                // Implementation would depend on file format
                // This is a placeholder for the actual import logic
                _logger.LogInformation("Data import completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing data");
                return false;
            }
        }

        private async Task<long> GetDatabaseSizeAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<Data.LMSDbContext>();

                var connectionString = context.Database.GetConnectionString();
                if (string.IsNullOrEmpty(connectionString))
                    return 0;

                // Extract database file path from SQLite connection string
                var dbPath = ExtractDatabasePath(connectionString);
                if (!string.IsNullOrEmpty(dbPath) && File.Exists(dbPath))
                {
                    var fileInfo = new FileInfo(dbPath);
                    return fileInfo.Length;
                }

                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private async Task CheckForIssuesAsync(DatabaseHealthStatus status)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<Data.LMSDbContext>();

                // Check for integrity issues (SQLite specific)
                var integrityResult = await context.Database
                    .SqlQueryRaw<string>("PRAGMA integrity_check")
                    .FirstOrDefaultAsync();

                if (integrityResult != "ok")
                {
                    status.Issues.Add($"Database integrity issue: {integrityResult}");
                    status.IsHealthy = false;
                }

                // Check for large number of failed sync records
                var failedSyncCount = await context.SyncRecords
                    .CountAsync(sr => sr.SyncStatus == Models.SyncStatus.Failed);

                if (failedSyncCount > 100)
                {
                    status.Issues.Add($"High number of failed sync records: {failedSyncCount}");
                }
            }
            catch (Exception ex)
            {
                status.Issues.Add($"Error checking for issues: {ex.Message}");
            }
        }

        private static string? ExtractDatabasePath(string connectionString)
        {
            var parts = connectionString.Split(';');
            foreach (var part in parts)
            {
                var keyValue = part.Split('=');
                if (keyValue.Length == 2 && 
                    keyValue[0].Trim().Equals("Data Source", StringComparison.OrdinalIgnoreCase))
                {
                    return keyValue[1].Trim();
                }
            }
            return null;
        }

        private async Task<bool> ExportToJsonAsync(LMSDbContext context, string filePath)
        {
            // Implementation for JSON export
            // This would serialize the data to JSON format
            await Task.CompletedTask;
            return true;
        }

        private async Task<bool> ExportToCsvAsync(LMSDbContext context, string filePath)
        {
            // Implementation for CSV export
            // This would export data to CSV format
            await Task.CompletedTask;
            return true;
        }

        private async Task<bool> ExportToSqlAsync(LMSDbContext context, string filePath)
        {
            // Implementation for SQL export
            // This would generate SQL INSERT statements
            await Task.CompletedTask;
            return true;
        }
    }
}
