using LMSCBTSystem.Models;

namespace LMSCBTSystem.Services
{
    /// <summary>
    /// Interface for authentication service
    /// </summary>
    public interface IAuthService
    {
        /// <summary>
        /// Authenticate user with username/email and password
        /// </summary>
        Task<AuthResult> AuthenticateAsync(string usernameOrEmail, string password);

        /// <summary>
        /// Register a new user
        /// </summary>
        Task<AuthResult> RegisterAsync(User user, string password);

        /// <summary>
        /// Change user password
        /// </summary>
        Task<bool> ChangePasswordAsync(long userId, string currentPassword, string newPassword);

        /// <summary>
        /// Reset password with token
        /// </summary>
        Task<bool> ResetPasswordAsync(string token, string newPassword);

        /// <summary>
        /// Generate password reset token
        /// </summary>
        Task<string?> GeneratePasswordResetTokenAsync(string email);

        /// <summary>
        /// Verify email with token
        /// </summary>
        Task<bool> VerifyEmailAsync(string token);

        /// <summary>
        /// Generate email verification token
        /// </summary>
        Task<string?> GenerateEmailVerificationTokenAsync(long userId);

        /// <summary>
        /// Validate password strength
        /// </summary>
        ValidationResult ValidatePassword(string password);

        /// <summary>
        /// Check if user has permission
        /// </summary>
        Task<bool> HasPermissionAsync(long userId, string permission);

        /// <summary>
        /// Get current user
        /// </summary>
        Task<User?> GetCurrentUserAsync();

        /// <summary>
        /// Set current user
        /// </summary>
        void SetCurrentUser(User user);

        /// <summary>
        /// Login user async
        /// </summary>
        Task<AuthResult> LoginAsync(string usernameOrEmail, string password);

        /// <summary>
        /// Logout current user
        /// </summary>
        void Logout();

        /// <summary>
        /// Logout current user async
        /// </summary>
        Task LogoutAsync();

        /// <summary>
        /// Check if user is authenticated
        /// </summary>
        bool IsAuthenticated { get; }

        /// <summary>
        /// Get current user ID
        /// </summary>
        long? CurrentUserId { get; }

        /// <summary>
        /// Lock user account
        /// </summary>
        Task LockAccountAsync(long userId, TimeSpan lockDuration);

        /// <summary>
        /// Unlock user account
        /// </summary>
        Task UnlockAccountAsync(long userId);

        /// <summary>
        /// Record failed login attempt
        /// </summary>
        Task RecordFailedLoginAsync(string usernameOrEmail);

        /// <summary>
        /// Reset failed login attempts
        /// </summary>
        Task ResetFailedLoginAttemptsAsync(long userId);
    }

    /// <summary>
    /// Authentication result
    /// </summary>
    public class AuthResult
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public User? User { get; set; }
        public string? Token { get; set; }
        public DateTime? ExpiresAt { get; set; }

        public static AuthResult Successful(User user, string? token = null, DateTime? expiresAt = null)
        {
            return new AuthResult
            {
                Success = true,
                User = user,
                Token = token,
                ExpiresAt = expiresAt,
                Message = "Authentication successful"
            };
        }

        public static AuthResult Failed(string message)
        {
            return new AuthResult
            {
                Success = false,
                Message = message
            };
        }
    }

    /// <summary>
    /// Validation result
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();

        public static ValidationResult Valid()
        {
            return new ValidationResult { IsValid = true };
        }

        public static ValidationResult Invalid(params string[] errors)
        {
            return new ValidationResult
            {
                IsValid = false,
                Errors = errors.ToList()
            };
        }

        public void AddError(string error)
        {
            Errors.Add(error);
            IsValid = false;
        }
    }
}
