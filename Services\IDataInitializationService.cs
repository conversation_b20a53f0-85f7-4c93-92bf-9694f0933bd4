namespace LMSCBTSystem.Services
{
    /// <summary>
    /// Interface for data initialization service
    /// </summary>
    public interface IDataInitializationService
    {
        /// <summary>
        /// Check if system is initialized
        /// </summary>
        Task<bool> IsSystemInitializedAsync();

        /// <summary>
        /// Initialize default data
        /// </summary>
        Task InitializeDefaultDataAsync();

        /// <summary>
        /// Create default admin user
        /// </summary>
        Task CreateDefaultAdminUserAsync();

        /// <summary>
        /// Create default roles and permissions
        /// </summary>
        Task CreateDefaultRolesAsync();

        /// <summary>
        /// Create sample data for testing
        /// </summary>
        Task CreateSampleDataAsync();

        /// <summary>
        /// Reset system to default state
        /// </summary>
        Task ResetToDefaultAsync();

        /// <summary>
        /// Validate system integrity
        /// </summary>
        Task<List<string>> ValidateSystemIntegrityAsync();
    }
}
