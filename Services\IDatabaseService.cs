using LMSCBTSystem.Configuration;

namespace LMSCBTSystem.Services
{
    /// <summary>
    /// Interface for database service
    /// </summary>
    public interface IDatabaseService
    {
        /// <summary>
        /// Initialize database service
        /// </summary>
        Task InitializeAsync();

        /// <summary>
        /// Test database connection
        /// </summary>
        Task<bool> TestConnectionAsync();

        /// <summary>
        /// Get database statistics
        /// </summary>
        Task<DatabaseStatistics> GetStatisticsAsync();

        /// <summary>
        /// Backup database
        /// </summary>
        Task<bool> BackupAsync(string backupPath);

        /// <summary>
        /// Restore database from backup
        /// </summary>
        Task<bool> RestoreAsync(string backupPath);

        /// <summary>
        /// Check if database is initialized
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// Check if database is online
        /// </summary>
        bool IsOnline { get; }

        /// <summary>
        /// Get database health status
        /// </summary>
        Task<DatabaseHealthStatus> GetHealthStatusAsync();

        /// <summary>
        /// Optimize database
        /// </summary>
        Task OptimizeDatabaseAsync();

        /// <summary>
        /// Clean up old data
        /// </summary>
        Task CleanupOldDataAsync(TimeSpan retentionPeriod);

        /// <summary>
        /// Export data to file
        /// </summary>
        Task<bool> ExportDataAsync(string filePath, ExportFormat format);

        /// <summary>
        /// Import data from file
        /// </summary>
        Task<bool> ImportDataAsync(string filePath, ImportOptions options);
    }

    /// <summary>
    /// Database health status
    /// </summary>
    public class DatabaseHealthStatus
    {
        public bool IsHealthy { get; set; }
        public string Status { get; set; } = string.Empty;
        public TimeSpan ResponseTime { get; set; }
        public long DatabaseSize { get; set; }
        public DateTime LastChecked { get; set; }
        public List<string> Issues { get; set; } = new List<string>();

        public string DatabaseSizeFormatted
        {
            get
            {
                var size = DatabaseSize;
                string[] sizes = { "B", "KB", "MB", "GB" };
                int order = 0;
                while (size >= 1024 && order < sizes.Length - 1)
                {
                    order++;
                    size = size / 1024;
                }
                return $"{size:0.##} {sizes[order]}";
            }
        }
    }

    /// <summary>
    /// Export format options
    /// </summary>
    public enum ExportFormat
    {
        Json,
        Csv,
        Excel,
        Xml,
        Sql
    }

    /// <summary>
    /// Import options
    /// </summary>
    public class ImportOptions
    {
        public bool OverwriteExisting { get; set; } = false;
        public bool ValidateData { get; set; } = true;
        public bool CreateBackup { get; set; } = true;
        public List<string> TablesToImport { get; set; } = new List<string>();
        public Dictionary<string, string> FieldMappings { get; set; } = new Dictionary<string, string>();
    }
}
