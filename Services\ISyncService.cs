using LMSCBTSystem.Models;

namespace LMSCBTSystem.Services
{
    /// <summary>
    /// Interface for synchronization service
    /// </summary>
    public interface ISyncService
    {
        /// <summary>
        /// Start synchronization process
        /// </summary>
        Task<SyncResult> StartSyncAsync();

        /// <summary>
        /// Stop synchronization process
        /// </summary>
        Task StopSyncAsync();

        /// <summary>
        /// Sync specific entity
        /// </summary>
        Task<bool> SyncEntityAsync(string entityType, long entityId, OperationType operation);

        /// <summary>
        /// Get sync status
        /// </summary>
        Task<SyncStatus> GetSyncStatusAsync();

        /// <summary>
        /// Get pending sync records
        /// </summary>
        Task<List<SyncRecord>> GetPendingSyncRecordsAsync();

        /// <summary>
        /// Retry failed sync records
        /// </summary>
        Task<int> RetryFailedSyncsAsync();

        /// <summary>
        /// Clear completed sync records
        /// </summary>
        Task<int> ClearCompletedSyncRecordsAsync();

        /// <summary>
        /// Check if sync is running
        /// </summary>
        bool IsSyncRunning { get; }

        /// <summary>
        /// Get last sync time
        /// </summary>
        DateTime? LastSyncTime { get; }

        /// <summary>
        /// Sync progress event
        /// </summary>
        event EventHandler<SyncProgressEventArgs>? SyncProgress;

        /// <summary>
        /// Sync completed event
        /// </summary>
        event EventHandler<SyncCompletedEventArgs>? SyncCompleted;
    }

    /// <summary>
    /// Sync result
    /// </summary>
    public class SyncResult
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public int TotalRecords { get; set; }
        public int SyncedRecords { get; set; }
        public int FailedRecords { get; set; }
        public TimeSpan Duration { get; set; }
        public List<string> Errors { get; set; } = new List<string>();

        public static SyncResult Successful(int totalRecords, int syncedRecords, TimeSpan duration)
        {
            return new SyncResult
            {
                Success = true,
                TotalRecords = totalRecords,
                SyncedRecords = syncedRecords,
                Duration = duration,
                Message = "Synchronization completed successfully"
            };
        }

        public static SyncResult Failed(string message, List<string>? errors = null)
        {
            return new SyncResult
            {
                Success = false,
                Message = message,
                Errors = errors ?? new List<string>()
            };
        }
    }

    /// <summary>
    /// Sync progress event arguments
    /// </summary>
    public class SyncProgressEventArgs : EventArgs
    {
        public int TotalRecords { get; set; }
        public int ProcessedRecords { get; set; }
        public string? CurrentEntity { get; set; }
        public double ProgressPercentage => TotalRecords > 0 ? (double)ProcessedRecords / TotalRecords * 100 : 0;
    }

    /// <summary>
    /// Sync completed event arguments
    /// </summary>
    public class SyncCompletedEventArgs : EventArgs
    {
        public SyncResult Result { get; set; } = new SyncResult();
    }
}
