using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using LMSCBTSystem.Models;
using System.Diagnostics;

namespace LMSCBTSystem.Services
{
    /// <summary>
    /// Synchronization service implementation
    /// </summary>
    public class SyncService : ISyncService
    {
        private readonly LMSDbContext _context;
        private readonly ILogger<SyncService> _logger;
        private bool _isSyncRunning = false;
        private DateTime? _lastSyncTime;
        private CancellationTokenSource? _cancellationTokenSource;

        public SyncService(LMSDbContext context, ILogger<SyncService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public bool IsSyncRunning => _isSyncRunning;
        public DateTime? LastSyncTime => _lastSyncTime;

        public event EventHandler<SyncProgressEventArgs>? SyncProgress;
        public event EventHandler<SyncCompletedEventArgs>? SyncCompleted;

        public async Task<SyncResult> StartSyncAsync()
        {
            if (_isSyncRunning)
            {
                return SyncResult.Failed("Synchronization is already running");
            }

            _isSyncRunning = true;
            _cancellationTokenSource = new CancellationTokenSource();
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.LogInformation("Starting synchronization process...");

                var pendingRecords = await GetPendingSyncRecordsAsync();
                var totalRecords = pendingRecords.Count;
                var syncedRecords = 0;
                var failedRecords = 0;

                _logger.LogInformation("Found {TotalRecords} pending sync records", totalRecords);

                for (int i = 0; i < pendingRecords.Count; i++)
                {
                    if (_cancellationTokenSource.Token.IsCancellationRequested)
                    {
                        _logger.LogInformation("Synchronization cancelled");
                        break;
                    }

                    var record = pendingRecords[i];

                    try
                    {
                        // Report progress
                        SyncProgress?.Invoke(this, new SyncProgressEventArgs
                        {
                            TotalRecords = totalRecords,
                            ProcessedRecords = i + 1,
                            CurrentEntity = $"{record.EntityType}:{record.EntityId}"
                        });

                        // Process sync record
                        var success = await ProcessSyncRecordAsync(record);
                        if (success)
                        {
                            syncedRecords++;
                        }
                        else
                        {
                            failedRecords++;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing sync record {RecordId}", record.Id);
                        failedRecords++;
                    }
                }

                stopwatch.Stop();
                _lastSyncTime = DateTime.UtcNow;

                var result = SyncResult.Successful(totalRecords, syncedRecords, stopwatch.Elapsed);
                result.FailedRecords = failedRecords;

                _logger.LogInformation("Synchronization completed: {SyncedRecords}/{TotalRecords} records synced in {Duration}",
                    syncedRecords, totalRecords, stopwatch.Elapsed);

                SyncCompleted?.Invoke(this, new SyncCompletedEventArgs { Result = result });
                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Error during synchronization");

                var result = SyncResult.Failed($"Synchronization failed: {ex.Message}");
                result.Duration = stopwatch.Elapsed;

                SyncCompleted?.Invoke(this, new SyncCompletedEventArgs { Result = result });
                return result;
            }
            finally
            {
                _isSyncRunning = false;
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
        }

        public async Task StopSyncAsync()
        {
            if (_isSyncRunning && _cancellationTokenSource != null)
            {
                _logger.LogInformation("Stopping synchronization...");
                _cancellationTokenSource.Cancel();

                // Wait for sync to stop (with timeout)
                var timeout = TimeSpan.FromSeconds(30);
                var startTime = DateTime.UtcNow;

                while (_isSyncRunning && DateTime.UtcNow - startTime < timeout)
                {
                    await Task.Delay(100);
                }

                if (_isSyncRunning)
                {
                    _logger.LogWarning("Synchronization did not stop within timeout period");
                }
                else
                {
                    _logger.LogInformation("Synchronization stopped successfully");
                }
            }
        }

        public async Task<bool> SyncEntityAsync(string entityType, long entityId, OperationType operation)
        {
            try
            {
                _logger.LogDebug("Creating sync record for {EntityType}:{EntityId} ({Operation})", 
                    entityType, entityId, operation);

                var syncRecord = new SyncRecord
                {
                    EntityType = entityType,
                    EntityId = entityId,
                    OperationType = operation,
                    SyncStatus = Models.SyncStatus.Pending,
                    Priority = GetPriorityForOperation(operation)
                };

                _context.SyncRecords.Add(syncRecord);
                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating sync record for {EntityType}:{EntityId}", entityType, entityId);
                return false;
            }
        }

        public async Task<Models.SyncStatus> GetSyncStatusAsync()
        {
            try
            {
                var hasPending = await _context.SyncRecords
                    .AnyAsync(sr => sr.SyncStatus == Models.SyncStatus.Pending);

                var hasInProgress = await _context.SyncRecords
                    .AnyAsync(sr => sr.SyncStatus == Models.SyncStatus.InProgress);

                if (hasInProgress || _isSyncRunning)
                    return Models.SyncStatus.InProgress;

                if (hasPending)
                    return Models.SyncStatus.Pending;

                return Models.SyncStatus.Completed;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sync status");
                return Models.SyncStatus.Failed;
            }
        }

        public async Task<List<SyncRecord>> GetPendingSyncRecordsAsync()
        {
            try
            {
                return await _context.SyncRecords
                    .Where(sr => sr.SyncStatus == Models.SyncStatus.Pending || 
                                (sr.SyncStatus == Models.SyncStatus.Failed && sr.CanRetry))
                    .OrderBy(sr => sr.Priority)
                    .ThenBy(sr => sr.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending sync records");
                return new List<SyncRecord>();
            }
        }

        public async Task<int> RetryFailedSyncsAsync()
        {
            try
            {
                var failedRecords = await _context.SyncRecords
                    .Where(sr => sr.SyncStatus == Models.SyncStatus.Failed && sr.CanRetry)
                    .ToListAsync();

                foreach (var record in failedRecords)
                {
                    record.ResetForRetry();
                }

                var retryCount = await _context.SaveChangesAsync();
                _logger.LogInformation("Reset {RetryCount} failed sync records for retry", retryCount);

                return retryCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrying failed sync records");
                return 0;
            }
        }

        public async Task<int> ClearCompletedSyncRecordsAsync()
        {
            try
            {
                var completedRecords = _context.SyncRecords
                    .Where(sr => sr.SyncStatus == Models.SyncStatus.Completed && 
                                sr.SyncCompletedAt < DateTime.UtcNow.AddDays(-7)); // Keep for 7 days

                _context.SyncRecords.RemoveRange(completedRecords);
                var deletedCount = await _context.SaveChangesAsync();

                _logger.LogInformation("Cleared {DeletedCount} completed sync records", deletedCount);
                return deletedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing completed sync records");
                return 0;
            }
        }

        private async Task<bool> ProcessSyncRecordAsync(SyncRecord record)
        {
            try
            {
                record.MarkAsStarted();
                await _context.SaveChangesAsync();

                // Simulate sync processing
                // In a real implementation, this would sync with an online server
                await Task.Delay(100); // Simulate network delay

                // For now, just mark as completed
                record.MarkAsCompleted();
                await _context.SaveChangesAsync();

                _logger.LogDebug("Sync record {RecordId} processed successfully", record.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing sync record {RecordId}", record.Id);

                record.MarkAsFailed(ex.Message);
                await _context.SaveChangesAsync();

                return false;
            }
        }

        private static int GetPriorityForOperation(OperationType operation)
        {
            return operation switch
            {
                OperationType.Delete => 1, // Highest priority
                OperationType.Update => 2,
                OperationType.Create => 3,
                OperationType.Sync => 4,   // Lowest priority
                _ => 5
            };
        }
    }
}
