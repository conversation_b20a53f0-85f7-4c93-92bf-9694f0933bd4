using Microsoft.Extensions.Logging;
using System.Windows;
using System.Windows.Input;
using LMSCBTSystem.Services;
using LMSCBTSystem.Views;

namespace LMSCBTSystem.ViewModels
{
    /// <summary>
    /// Login view model
    /// </summary>
    public class LoginViewModel : BaseViewModel
    {
        private readonly IAuthService _authService;
        private readonly ILogger<LoginViewModel> _logger;

        private string _username = string.Empty;
        private string _password = string.Empty;
        private bool _rememberMe = false;
        private bool _isLoading = false;
        private string _errorMessage = string.Empty;
        private bool _hasError = false;

        public LoginViewModel(IAuthService authService, ILogger<LoginViewModel> logger)
        {
            _authService = authService;
            _logger = logger;

            LoginCommand = new AsyncRelayCommand(_ => LoginAsync(), _ => CanLogin);
            ForgotPasswordCommand = new RelayCommand(ForgotPassword);
        }

        #region Properties

        public string Username
        {
            get => _username;
            set => SetProperty(ref _username, value, _ => LoginCommand.RaiseCanExecuteChanged());
        }

        public string Password
        {
            get => _password;
            set => SetProperty(ref _password, value, _ => LoginCommand.RaiseCanExecuteChanged());
        }

        public bool RememberMe
        {
            get => _rememberMe;
            set => SetProperty(ref _rememberMe, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        public bool HasError
        {
            get => _hasError;
            set => SetProperty(ref _hasError, value);
        }

        public bool CanLogin => !IsLoading && !string.IsNullOrWhiteSpace(Username) && !string.IsNullOrWhiteSpace(Password);

        #endregion

        #region Commands

        public AsyncRelayCommand LoginCommand { get; }
        public ICommand ForgotPasswordCommand { get; }

        #endregion

        #region Methods

        private async Task LoginAsync()
        {
            try
            {
                IsLoading = true;
                ClearError();

                _logger.LogInformation("Attempting login for user: {Username}", Username);

                var result = await _authService.LoginAsync(Username, Password);

                if (result.Success && result.User != null)
                {
                    _logger.LogInformation("Login successful for user: {Username}", Username);

                    // Save remember me preference if needed
                    if (RememberMe)
                    {
                        // TODO: Implement remember me functionality
                        // This could save encrypted credentials or a refresh token
                    }

                    // Navigate to main window
                    await NavigateToMainWindow();
                }
                else
                {
                    _logger.LogWarning("Login failed for user: {Username}. Reason: {Message}", Username, result.Message);
                    ShowError(result.Message ?? "Login failed. Please check your credentials.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for user: {Username}", Username);
                ShowError("An error occurred during login. Please try again.");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task NavigateToMainWindow()
        {
            try
            {
                // Get current window
                var currentWindow = Application.Current.Windows.OfType<LoginWindow>().FirstOrDefault();

                // Create main window
                var mainWindow = App.GetService<MainWindow>();
                if (mainWindow == null)
                {
                    // Fallback: create main window manually
                    var mainViewModel = App.GetService<MainViewModel>();
                    mainWindow = new MainWindow { DataContext = mainViewModel };
                }

                // Show main window
                Application.Current.MainWindow = mainWindow;
                mainWindow.Show();

                // Close login window
                currentWindow?.Close();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error navigating to main window");
                ShowError("Failed to open main window. Please try again.");
            }
        }

        private void ForgotPassword()
        {
            try
            {
                _logger.LogInformation("Forgot password requested");

                // TODO: Implement forgot password functionality
                // This could open a dialog or navigate to a forgot password window
                App.ShowInfo("Forgot password functionality will be implemented soon.", "Coming Soon");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in forgot password");
                ShowError("An error occurred. Please try again.");
            }
        }

        private void ShowError(string message)
        {
            ErrorMessage = message;
            HasError = true;
        }

        private void ClearError()
        {
            ErrorMessage = string.Empty;
            HasError = false;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Initialize view model
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                _logger.LogDebug("Initializing login view model");

                // Check if user is already logged in
                var currentUser = await _authService.GetCurrentUserAsync();
                if (currentUser != null)
                {
                    _logger.LogInformation("User already logged in: {Username}", currentUser.Username);
                    await NavigateToMainWindow();
                    return;
                }

                // Load remember me settings if available
                // TODO: Implement remember me loading
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing login view model");
            }
        }

        /// <summary>
        /// Clear form
        /// </summary>
        public void ClearForm()
        {
            Username = string.Empty;
            Password = string.Empty;
            RememberMe = false;
            ClearError();
        }

        #endregion
    }
}
