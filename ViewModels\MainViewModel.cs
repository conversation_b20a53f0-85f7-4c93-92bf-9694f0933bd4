using Microsoft.Extensions.Logging;
using System.Windows.Input;
using System.Windows.Threading;
using LMSCBTSystem.Services;
using LMSCBTSystem.Models;

namespace LMSCBTSystem.ViewModels
{
    /// <summary>
    /// Main window view model
    /// </summary>
    public class MainViewModel : BaseViewModel
    {
        private readonly IAuthService _authService;
        private readonly ISyncService _syncService;
        private readonly ILogger<MainViewModel> _logger;
        private readonly DispatcherTimer _timer;

        private User? _currentUser;
        private object? _currentViewModel;
        private bool _isLoading = false;
        private bool _isMenuOpen = true;
        private string _statusMessage = "Ready";
        private string _syncStatus = "Synchronized";
        private string _currentTime = string.Empty;

        public MainViewModel(
            IAuthService authService,
            ISyncService syncService,
            ILogger<MainViewModel> logger)
        {
            _authService = authService;
            _syncService = syncService;
            _logger = logger;

            // Initialize commands
            NavigateCommand = new RelayCommand(param => Navigate(param?.ToString() ?? string.Empty));
            ToggleMenuCommand = new RelayCommand(ToggleMenu);
            UserMenuCommand = new RelayCommand(ShowUserMenu);
            LogoutCommand = new AsyncRelayCommand(LogoutAsync);

            // Initialize timer for status updates
            _timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _timer.Tick += Timer_Tick;
            _timer.Start();

            // Set initial current time
            UpdateCurrentTime();
        }

        #region Properties

        public User? CurrentUser
        {
            get => _currentUser;
            set => SetProperty(ref _currentUser, value);
        }

        public object? CurrentViewModel
        {
            get => _currentViewModel;
            set => SetProperty(ref _currentViewModel, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public bool IsMenuOpen
        {
            get => _isMenuOpen;
            set => SetProperty(ref _isMenuOpen, value);
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public string SyncStatus
        {
            get => _syncStatus;
            set => SetProperty(ref _syncStatus, value);
        }

        public string CurrentTime
        {
            get => _currentTime;
            set => SetProperty(ref _currentTime, value);
        }

        public string WindowTitle => $"LMS CBT System - {CurrentUser?.FullName ?? "User"}";

        public double MenuWidth => IsMenuOpen ? 250 : 0;

        public bool CanManageUsers => CurrentUser?.Role?.Name == "Administrator" || CurrentUser?.Role?.Name == "Staff";

        #endregion

        #region Commands

        public ICommand NavigateCommand { get; }
        public ICommand ToggleMenuCommand { get; }
        public ICommand UserMenuCommand { get; }
        public AsyncRelayCommand LogoutCommand { get; }

        #endregion

        #region Methods

        public async Task InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Initializing main view model");

                IsLoading = true;
                StatusMessage = "Loading...";

                // Get current user
                CurrentUser = await _authService.GetCurrentUserAsync();
                if (CurrentUser == null)
                {
                    _logger.LogWarning("No current user found, redirecting to login");
                    await LogoutAsync();
                    return;
                }

                _logger.LogInformation("Current user: {Username} ({Role})", CurrentUser.Username, CurrentUser.Role?.Name);

                // Navigate to dashboard by default
                Navigate("Dashboard");

                // Update sync status
                await UpdateSyncStatusAsync();

                StatusMessage = "Ready";
                OnPropertyChanged(nameof(WindowTitle));
                OnPropertyChanged(nameof(CanManageUsers));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing main view model");
                StatusMessage = "Error loading application";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void Navigate(string? destination)
        {
            try
            {
                if (string.IsNullOrEmpty(destination))
                    return;

                _logger.LogDebug("Navigating to: {Destination}", destination);

                // Create appropriate view model based on destination
                CurrentViewModel = destination switch
                {
                    "Dashboard" => App.GetService<DashboardViewModel>(),
                    "Courses" => App.GetService<CourseManagementViewModel>(),
                    "Tests" => App.GetService<TestManagementViewModel>(),
                    "Users" => App.GetService<UserManagementViewModel>(),
                    "Reports" => CreateReportsViewModel(),
                    "Settings" => CreateSettingsViewModel(),
                    _ => CreateNotFoundViewModel(destination)
                };

                StatusMessage = $"Navigated to {destination}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error navigating to {Destination}", destination);
                StatusMessage = $"Error navigating to {destination}";
            }
        }

        private void ToggleMenu()
        {
            IsMenuOpen = !IsMenuOpen;
            OnPropertyChanged(nameof(MenuWidth));
        }

        private void ShowUserMenu()
        {
            // TODO: Implement user menu (profile, settings, etc.)
            _logger.LogDebug("User menu requested");
        }

        private async Task LogoutAsync()
        {
            try
            {
                _logger.LogInformation("Logging out user: {Username}", CurrentUser?.Username);

                IsLoading = true;
                StatusMessage = "Logging out...";

                await _authService.LogoutAsync();

                // Navigate back to login
                var loginWindow = App.GetService<Views.LoginWindow>();
                if (loginWindow == null)
                {
                    var loginViewModel = App.GetService<LoginViewModel>();
                    loginWindow = new Views.LoginWindow { DataContext = loginViewModel };
                }

                System.Windows.Application.Current.MainWindow = loginWindow;
                loginWindow.Show();

                // Close current window
                var currentWindow = System.Windows.Application.Current.Windows
                    .OfType<Views.MainWindow>()
                    .FirstOrDefault();
                currentWindow?.Close();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
                StatusMessage = "Error during logout";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task UpdateSyncStatusAsync()
        {
            try
            {
                var status = await _syncService.GetSyncStatusAsync();
                SyncStatus = status switch
                {
                    Models.SyncStatus.Pending => "Sync Pending",
                    Models.SyncStatus.InProgress => "Syncing...",
                    Models.SyncStatus.Completed => "Synchronized",
                    Models.SyncStatus.Failed => "Sync Failed",
                    _ => "Unknown"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating sync status");
                SyncStatus = "Sync Error";
            }
        }

        private void Timer_Tick(object? sender, EventArgs e)
        {
            UpdateCurrentTime();

            // Periodically update sync status
            if (DateTime.Now.Second % 30 == 0) // Every 30 seconds
            {
                _ = Task.Run(UpdateSyncStatusAsync);
            }
        }

        private void UpdateCurrentTime()
        {
            CurrentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        public async Task<bool> CanCloseAsync()
        {
            try
            {
                // Check if there are any unsaved changes or running operations
                if (_syncService.IsSyncRunning)
                {
                    var result = System.Windows.MessageBox.Show(
                        "Synchronization is in progress. Are you sure you want to exit?",
                        "Confirm Exit",
                        System.Windows.MessageBoxButton.YesNo,
                        System.Windows.MessageBoxImage.Question);

                    return result == System.Windows.MessageBoxResult.Yes;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if window can close");
                return true;
            }
        }

        #endregion

        #region Helper Methods

        private object CreateReportsViewModel()
        {
            // TODO: Create actual reports view model
            return new { Title = "Reports", Content = "Reports functionality coming soon..." };
        }

        private object CreateSettingsViewModel()
        {
            // TODO: Create actual settings view model
            return new { Title = "Settings", Content = "Settings functionality coming soon..." };
        }

        private object CreateNotFoundViewModel(string destination)
        {
            return new { Title = "Not Found", Content = $"Page '{destination}' not found." };
        }

        #endregion

        #region Cleanup

        protected override void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            base.OnPropertyChanged(propertyName);
        }

        public void Dispose()
        {
            _timer?.Stop();
        }

        #endregion
    }

    /// <summary>
    /// Placeholder view models for missing implementations
    /// </summary>
    public class DashboardViewModel : BaseViewModel
    {
        public string Title => "Dashboard";
        public string Content => "Welcome to the LMS CBT System Dashboard!";
    }

    public class CourseManagementViewModel : BaseViewModel
    {
        public string Title => "Course Management";
        public string Content => "Course management functionality coming soon...";
    }

    public class TestManagementViewModel : BaseViewModel
    {
        public string Title => "Test Management";
        public string Content => "Test management functionality coming soon...";
    }

    public class UserManagementViewModel : BaseViewModel
    {
        public string Title => "User Management";
        public string Content => "User management functionality coming soon...";
    }
}
