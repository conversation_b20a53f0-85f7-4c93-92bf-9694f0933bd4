<Window x:Class="LMSCBTSystem.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="LMS CBT System - Login" 
        Height="600" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="0,0,0,16" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth2">
            <StackPanel Orientation="Vertical" Margin="24">
                <materialDesign:PackIcon Kind="School" Width="48" Height="48" 
                                       HorizontalAlignment="Center"
                                       Foreground="{StaticResource PrimaryBrush}"/>
                <TextBlock Text="LMS CBT System" 
                         Style="{StaticResource TitleTextStyle}"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
                <TextBlock Text="Learning Management System with Computer-Based Testing" 
                         Style="{StaticResource BodyTextStyle}"
                         HorizontalAlignment="Center"
                         TextAlignment="Center"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Login Form -->
        <materialDesign:Card Grid.Row="1" Margin="24,0" 
                           Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Vertical" Margin="16">
                <TextBlock Text="Sign In" 
                         Style="{StaticResource SubtitleTextStyle}"
                         HorizontalAlignment="Center"
                         Margin="0,0,0,24"/>

                <!-- Username/Email -->
                <TextBox x:Name="UsernameTextBox"
                       materialDesign:HintAssist.Hint="Username or Email"
                       materialDesign:HintAssist.IsFloating="True"
                       Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                       Margin="0,0,0,16"
                       Style="{StaticResource MaterialDesignOutlinedTextBox}"/>

                <!-- Password -->
                <PasswordBox x:Name="PasswordBox"
                           materialDesign:HintAssist.Hint="Password"
                           materialDesign:HintAssist.IsFloating="True"
                           materialDesign:PasswordBoxAssist.Password="{Binding Password, UpdateSourceTrigger=PropertyChanged}"
                           Margin="0,0,0,16"
                           Style="{StaticResource MaterialDesignOutlinedPasswordBox}"/>

                <!-- Remember Me -->
                <CheckBox Content="Remember me" 
                        IsChecked="{Binding RememberMe}"
                        Margin="0,0,0,24"/>

                <!-- Error Message -->
                <TextBlock Text="{Binding ErrorMessage}" 
                         Foreground="{StaticResource ErrorBrush}"
                         Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}"
                         TextWrapping="Wrap"
                         Margin="0,0,0,16"/>

                <!-- Login Button -->
                <Button Content="SIGN IN" 
                      Command="{Binding LoginCommand}"
                      Style="{StaticResource PrimaryButtonStyle}"
                      IsEnabled="{Binding CanLogin}"
                      Margin="0,0,0,16"/>

                <!-- Loading Indicator -->
                <ProgressBar IsIndeterminate="True"
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                           Margin="0,0,0,16"/>

                <!-- Forgot Password -->
                <Button Content="Forgot Password?" 
                      Command="{Binding ForgotPasswordCommand}"
                      Style="{StaticResource MaterialDesignFlatButton}"
                      HorizontalAlignment="Center"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Footer -->
        <StackPanel Grid.Row="2" Orientation="Vertical" 
                  HorizontalAlignment="Center" 
                  Margin="24">
            <TextBlock Text="© 2024 LMS CBT System" 
                     Style="{StaticResource BodyTextStyle}"
                     HorizontalAlignment="Center"
                     Opacity="0.7"/>
            <TextBlock Text="Version 1.0.0" 
                     Style="{StaticResource BodyTextStyle}"
                     HorizontalAlignment="Center"
                     Opacity="0.5"
                     FontSize="12"/>
        </StackPanel>
    </Grid>
</Window>
