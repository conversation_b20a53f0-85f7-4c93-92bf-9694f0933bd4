{"App": {"Name": "LMS CBT System", "Version": "1.0.0", "Title": "Learning Management System with CBT"}, "Database": {"Offline": {"ConnectionString": "Data Source=data/lms_offline.db;Cache=Shared"}, "Online": {"ConnectionString": "Server=localhost;Database=lms_cbt;User=lms_user;Password=lms_password;", "Provider": "MySQL"}}, "Security": {"Jwt": {"Secret": "lms-cbt-secret-key-2024-very-long-secret-for-security", "ExpirationHours": 24, "Issuer": "LMSCBTSystem", "Audience": "LMSCBTUsers"}, "Password": {"MinLength": 8, "RequireSpecialChars": true, "RequireNumbers": true, "RequireUppercase": true, "RequireLowercase": true}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "File": {"Path": "logs/lms-cbt-.log", "RollingInterval": "Day", "RetainedFileCountLimit": 30}}, "UI": {"Theme": "Modern", "AnimationDuration": 300, "DefaultLanguage": "en-US"}}