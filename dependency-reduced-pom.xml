<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.lmssoft</groupId>
  <artifactId>lms-cbt-system</artifactId>
  <name>LMS CBT System</name>
  <version>1.0.0</version>
  <description>Learning Management System with Computer-Based Testing for Secondary Schools</description>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.11.0</version>
        <configuration>
          <source>17</source>
          <target>17</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.openjfx</groupId>
        <artifactId>javafx-maven-plugin</artifactId>
        <version>0.0.8</version>
        <configuration>
          <mainClass>com.lmssoft.LMSApplication</mainClass>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <version>3.6.0</version>
        <executions>
          <execution>
            <id>make-assembly</id>
            <phase>package</phase>
            <goals>
              <goal>single</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <descriptorRefs>
            <descriptorRef>jar-with-dependencies</descriptorRef>
          </descriptorRefs>
          <archive>
            <manifest>
              <mainClass>com.lmssoft.LMSApplication</mainClass>
            </manifest>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-jlink-plugin</artifactId>
        <version>3.1.0</version>
        <configuration>
          <moduleName>lms.cbt.system</moduleName>
          <launcher>lms-cbt-system=lms.cbt.system/com.lmssoft.LMSApplication</launcher>
          <compress>2</compress>
          <noHeaderFiles>true</noHeaderFiles>
          <noManPages>true</noManPages>
          <stripDebug>true</stripDebug>
          <addModules>
            <addModule>java.base</addModule>
            <addModule>java.desktop</addModule>
            <addModule>java.logging</addModule>
            <addModule>java.naming</addModule>
            <addModule>java.sql</addModule>
            <addModule>javafx.controls</addModule>
            <addModule>javafx.fxml</addModule>
            <addModule>javafx.web</addModule>
            <addModule>javafx.media</addModule>
          </addModules>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.1.2</version>
        <configuration>
          <argLine>--add-opens java.base/java.lang=ALL-UNNAMED
                        --add-opens java.base/java.util=ALL-UNNAMED
                        --add-opens java.desktop/java.awt=ALL-UNNAMED
                        --add-opens javafx.graphics/javafx.scene=ALL-UNNAMED
                        --add-opens javafx.controls/javafx.scene.control=ALL-UNNAMED</argLine>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.5.0</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>module-info.class</exclude>
                    <exclude>META-INF/versions/*/module-info.class</exclude>
                  </excludes>
                </filter>
              </filters>
              <transformers>
                <transformer>
                  <mainClass>com.lmssoft.LMSApplication</mainClass>
                </transformer>
              </transformers>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <version>2.2.220</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>5.10.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-jupiter-api</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-jupiter-params</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-jupiter-engine</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>5.5.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>byte-buddy-agent</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>objenesis</artifactId>
          <groupId>org.objenesis</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.testfx</groupId>
      <artifactId>testfx-junit5</artifactId>
      <version>4.0.16-alpha</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>testfx-core</artifactId>
          <groupId>org.testfx</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <properties>
    <jackson.version>2.15.2</jackson.version>
    <fontawesomefx.version>8.9</fontawesomefx.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <javafx.version>19.0.2.1</javafx.version>
    <junit.version>5.10.0</junit.version>
    <logback.version>1.4.11</logback.version>
    <controlsfx.version>11.1.2</controlsfx.version>
    <sqlite.version>3.42.0.0</sqlite.version>
    <hibernate.version>6.2.7.Final</hibernate.version>
    <bcrypt.version>0.10.2</bcrypt.version>
    <maven.compiler.source>17</maven.compiler.source>
    <h2.version>2.2.220</h2.version>
    <maven.compiler.target>17</maven.compiler.target>
  </properties>
</project>
