# PowerShell script to create a WORKING EXE for LMS CBT System
Write-Host "Creating FINAL WORKING EXE for LMS CBT System..." -ForegroundColor Green

# Check if JAR exists
$jarFile = "target\lms-cbt-system-1.0.0-jar-with-dependencies.jar"
if (-not (Test-Path $jarFile)) {
    Write-Host "ERROR: JAR file not found at $jarFile" -ForegroundColor Red
    exit 1
}

Write-Host "JAR file found: $jarFile" -ForegroundColor Green

# Test the JAR directly with proper JavaFX arguments
Write-Host ""
Write-Host "Testing JAR with proper JavaFX arguments..." -ForegroundColor Yellow

$javaArgs = @(
    "-Djava.awt.headless=false",
    "-Dfile.encoding=UTF-8",
    "-Dprism.order=sw",
    "-Dprism.verbose=true",
    "-jar",
    $jarFile
)

Write-Host "Running: java $($javaArgs -join ' ')" -ForegroundColor Cyan

try {
    $process = Start-Process -FilePath "java" -ArgumentList $javaArgs -PassThru -WindowStyle Normal
    Start-Sleep -Seconds 8
    
    if (-not $process.HasExited) {
        Write-Host "SUCCESS: JAR is running!" -ForegroundColor Green
        $process.Kill()
        $process.WaitForExit()
        
        # Now create jpackage with the working JAR
        Write-Host ""
        Write-Host "Creating native EXE with jpackage..." -ForegroundColor Yellow
        
        $outputDir = "target\final-app"
        if (Test-Path $outputDir) {
            Remove-Item $outputDir -Recurse -Force
        }
        New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
        
        $inputDir = "target\final-input"
        if (Test-Path $inputDir) {
            Remove-Item $inputDir -Recurse -Force
        }
        New-Item -ItemType Directory -Path $inputDir -Force | Out-Null
        
        # Copy JAR
        Copy-Item $jarFile "$inputDir\app.jar" -Force
        
        # Create jpackage with minimal configuration
        $jpackageArgs = @(
            "--input", $inputDir,
            "--name", "LMS-CBT-System",
            "--main-jar", "app.jar",
            "--main-class", "com.lmssoft.LMSApplication",
            "--dest", $outputDir,
            "--type", "app-image",
            "--app-version", "1.0.0",
            "--vendor", "LMS Software",
            "--java-options", "-Djava.awt.headless=false",
            "--java-options", "-Dfile.encoding=UTF-8",
            "--java-options", "-Dprism.order=sw"
        )
        
        Write-Host "Running jpackage..." -ForegroundColor Cyan
        & jpackage @jpackageArgs
        
        if ($LASTEXITCODE -eq 0) {
            $nativeApp = "$outputDir\LMS-CBT-System\LMS-CBT-System.exe"
            Write-Host "SUCCESS: Native app created!" -ForegroundColor Green
            
            # Test the native app
            Write-Host ""
            Write-Host "Testing native app..." -ForegroundColor Yellow
            
            $nativeProcess = Start-Process -FilePath $nativeApp -PassThru -WindowStyle Normal
            Start-Sleep -Seconds 5
            
            if (-not $nativeProcess.HasExited) {
                Write-Host "SUCCESS: Native app is working!" -ForegroundColor Green
                $nativeProcess.Kill()
                $nativeProcess.WaitForExit()
            } else {
                Write-Host "WARNING: Native app exited. Exit code: $($nativeProcess.ExitCode)" -ForegroundColor Yellow
            }
        } else {
            Write-Host "ERROR: jpackage failed" -ForegroundColor Red
        }
        
        # Clean up
        if (Test-Path $inputDir) {
            Remove-Item $inputDir -Recurse -Force
        }
        
    } else {
        Write-Host "ERROR: JAR failed to run. Exit code: $($process.ExitCode)" -ForegroundColor Red
    }
}
catch {
    Write-Host "ERROR: Failed to test JAR: $($_.Exception.Message)" -ForegroundColor Red
}

# Create a guaranteed working launcher
Write-Host ""
Write-Host "Creating guaranteed working launcher..." -ForegroundColor Yellow

$workingLauncher = @"
@echo off
title LMS CBT System - Learning Management System
cls
echo ========================================
echo LMS CBT System
echo Learning Management System with CBT
echo ========================================
echo.
echo Starting application...
echo.

REM Change to the directory containing this script
cd /d "%~dp0"

REM Run the application with proper JavaFX settings
java -Djava.awt.headless=false -Dfile.encoding=UTF-8 -Dprism.order=sw -jar "lms-cbt-system-1.0.0-jar-with-dependencies.jar"

if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo Application failed to start
    echo ========================================
    echo.
    echo Error code: %ERRORLEVEL%
    echo.
    echo Possible solutions:
    echo 1. Install Java 17 or higher
    echo 2. Make sure JavaFX is available
    echo 3. Check if the JAR file exists
    echo.
    echo Default login credentials:
    echo Username: admin
    echo Password: Admin@123
    echo.
    pause
) else (
    echo.
    echo Application closed normally.
)
"@

$workingLauncher | Out-File -FilePath "target\LMS-CBT-System.bat" -Encoding ASCII

# Copy JAR to target for distribution
Copy-Item $jarFile "target\" -Force

Write-Host ""
Write-Host "=== FINAL SUMMARY ===" -ForegroundColor Cyan
Write-Host ""

$nativeApp = "target\final-app\LMS-CBT-System\LMS-CBT-System.exe"
$launcher = "target\LMS-CBT-System.bat"

if (Test-Path $nativeApp) {
    Write-Host "SUCCESS: NATIVE WINDOWS EXE CREATED!" -ForegroundColor Green
    Write-Host "Location: $nativeApp" -ForegroundColor White
    Write-Host ""
    Write-Host "Features:" -ForegroundColor Yellow
    Write-Host "  - Native Windows executable with bundled JRE" -ForegroundColor Green
    Write-Host "  - No Java installation required for users" -ForegroundColor Green
    Write-Host "  - Professional desktop application" -ForegroundColor Green
    Write-Host ""
    Write-Host "TO RUN:" -ForegroundColor Cyan
    Write-Host "  Double-click: $nativeApp" -ForegroundColor White
    Write-Host ""
    Write-Host "TO DISTRIBUTE:" -ForegroundColor Cyan
    Write-Host "  Share the folder: target\final-app\LMS-CBT-System\" -ForegroundColor White
    
    # Launch the native app
    Write-Host ""
    Write-Host "Launching native app for you..." -ForegroundColor Green
    Start-Process -FilePath $nativeApp
    
} elseif (Test-Path $launcher) {
    Write-Host "WORKING LAUNCHER CREATED!" -ForegroundColor Green
    Write-Host "Location: $launcher" -ForegroundColor White
    Write-Host ""
    Write-Host "Features:" -ForegroundColor Yellow
    Write-Host "  - Guaranteed to work with Java installed" -ForegroundColor Green
    Write-Host "  - Proper error handling and messages" -ForegroundColor Green
    Write-Host "  - Professional appearance" -ForegroundColor Green
    Write-Host ""
    Write-Host "TO RUN:" -ForegroundColor Cyan
    Write-Host "  Double-click: $launcher" -ForegroundColor White
    Write-Host ""
    Write-Host "TO DISTRIBUTE:" -ForegroundColor Cyan
    Write-Host "  Share both files:" -ForegroundColor White
    Write-Host "    - $launcher" -ForegroundColor White
    Write-Host "    - target\lms-cbt-system-1.0.0-jar-with-dependencies.jar" -ForegroundColor White
    
    # Launch the batch file
    Write-Host ""
    Write-Host "Launching application for you..." -ForegroundColor Green
    Start-Process -FilePath $launcher
    
} else {
    Write-Host "ERROR: Could not create working application" -ForegroundColor Red
}

Write-Host ""
Write-Host "Default login credentials:" -ForegroundColor Cyan
Write-Host "Username: admin" -ForegroundColor White
Write-Host "Password: Admin@123" -ForegroundColor White
