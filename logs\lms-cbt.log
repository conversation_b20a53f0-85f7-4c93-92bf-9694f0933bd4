2025-09-12 14:57:51.312 [main] INFO  com.lmssoft.LMSApplication - Starting LMS CBT System...
2025-09-12 14:57:51.468 [JavaFX-Launcher] INFO  com.lmssoft.LMSApplication - Initializing application components...
2025-09-12 14:57:51.469 [JavaFX-Launcher] INFO  com.lmssoft.LMSApplication - Created data directory: D:\workspace\.softwares\lmssoft\data
2025-09-12 14:57:51.734 [JavaFX-Launcher] INFO  com.lmssoft.config.AppConfig - Configuration loaded successfully
2025-09-12 14:57:51.737 [JavaFX-Launcher] INFO  com.lmssoft.service.DatabaseService - Initializing database service...
2025-09-12 14:57:51.748 [JavaFX-Launcher] INFO  com.lmssoft.config.DatabaseConfig - Creating offline database session factory...
2025-09-12 14:57:56.558 [JavaFX-Launcher] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-09-12 14:57:57.311 [JavaFX-Launcher] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-09-12 14:57:57.528 [JavaFX-Launcher] WARN  o.h.e.j.e.i.JdbcEnvironmentInitiator - HHH000342: Could not obtain connection to query metadata
org.hibernate.boot.registry.selector.spi.StrategySelectionException: Unable to resolve name [org.hibernate.dialect.SQLiteDialect] as strategy [org.hibernate.dialect.Dialect]
	at <EMAIL>/org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.selectStrategyImplementor(StrategySelectorImpl.java:154)
	at <EMAIL>/org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:236)
	at <EMAIL>/org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:189)
	at <EMAIL>/org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect(DialectFactoryImpl.java:123)
	at <EMAIL>/org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:86)
	at <EMAIL>/org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:224)
	at <EMAIL>/org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:34)
	at <EMAIL>/org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:119)
	at <EMAIL>/org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:264)
	at <EMAIL>/org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:239)
	at <EMAIL>/org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:216)
	at <EMAIL>/org.hibernate.boot.model.relational.Database.<init>(Database.java:45)
	at <EMAIL>/org.hibernate.boot.internal.InFlightMetadataCollectorImpl.getDatabase(InFlightMetadataCollectorImpl.java:230)
	at <EMAIL>/org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:198)
	at <EMAIL>/org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:166)
	at <EMAIL>/org.hibernate.boot.model.process.spi.MetadataBuildingProcess.build(MetadataBuildingProcess.java:125)
	at <EMAIL>/org.hibernate.boot.internal.MetadataBuilderImpl.build(MetadataBuilderImpl.java:451)
	at <EMAIL>/org.hibernate.boot.internal.MetadataBuilderImpl.build(MetadataBuilderImpl.java:102)
	at <EMAIL>/org.hibernate.cfg.Configuration.buildSessionFactory(Configuration.java:910)
	at lms.cbt.system/com.lmssoft.config.DatabaseConfig.createOfflineSessionFactory(DatabaseConfig.java:88)
	at lms.cbt.system/com.lmssoft.config.DatabaseConfig.getOfflineSessionFactory(DatabaseConfig.java:30)
	at lms.cbt.system/com.lmssoft.service.DatabaseService.initialize(DatabaseService.java:47)
	at lms.cbt.system/com.lmssoft.LMSApplication.init(LMSApplication.java:58)
	at javafx.graphics@19.0.2.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:825)
	at javafx.graphics@19.0.2.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.hibernate.boot.registry.classloading.spi.ClassLoadingException: Unable to load class [org.hibernate.dialect.SQLiteDialect]
	at <EMAIL>/org.hibernate.boot.registry.classloading.internal.ClassLoaderServiceImpl.classForName(ClassLoaderServiceImpl.java:123)
	at <EMAIL>/org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.selectStrategyImplementor(StrategySelectorImpl.java:150)
	... 25 common frames omitted
Caused by: java.lang.ClassNotFoundException: Could not load requested class : org.hibernate.dialect.SQLiteDialect
	at <EMAIL>/org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:215)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:587)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:467)
	at <EMAIL>/org.hibernate.boot.registry.classloading.internal.ClassLoaderServiceImpl.classForName(ClassLoaderServiceImpl.java:120)
	... 26 common frames omitted
Caused by: java.lang.Throwable: null
	at <EMAIL>/org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:208)
	... 31 common frames omitted
	Suppressed: java.lang.ClassNotFoundException: org.hibernate.dialect.SQLiteDialect
		at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
		at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
		at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520)
		at <EMAIL>/org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:205)
		... 31 common frames omitted
	Suppressed: java.lang.ClassNotFoundException: org.hibernate.dialect.SQLiteDialect
		at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
		at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
		at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520)
		at <EMAIL>/org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:205)
		... 31 common frames omitted
	Suppressed: java.lang.ClassNotFoundException: org.hibernate.dialect.SQLiteDialect
		at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
		at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
		at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520)
		at <EMAIL>/org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:205)
		... 31 common frames omitted
2025-09-12 14:57:57.539 [JavaFX-Launcher] ERROR com.lmssoft.config.DatabaseConfig - Failed to create offline database session factory
org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment] due to: Unable to resolve name [org.hibernate.dialect.SQLiteDialect] as strategy [org.hibernate.dialect.Dialect]
	at <EMAIL>/org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:277)
	at <EMAIL>/org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:239)
	at <EMAIL>/org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:216)
	at <EMAIL>/org.hibernate.boot.model.relational.Database.<init>(Database.java:45)
	at <EMAIL>/org.hibernate.boot.internal.InFlightMetadataCollectorImpl.getDatabase(InFlightMetadataCollectorImpl.java:230)
	at <EMAIL>/org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:198)
	at <EMAIL>/org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:166)
	at <EMAIL>/org.hibernate.boot.model.process.spi.MetadataBuildingProcess.build(MetadataBuildingProcess.java:125)
	at <EMAIL>/org.hibernate.boot.internal.MetadataBuilderImpl.build(MetadataBuilderImpl.java:451)
	at <EMAIL>/org.hibernate.boot.internal.MetadataBuilderImpl.build(MetadataBuilderImpl.java:102)
	at <EMAIL>/org.hibernate.cfg.Configuration.buildSessionFactory(Configuration.java:910)
	at lms.cbt.system/com.lmssoft.config.DatabaseConfig.createOfflineSessionFactory(DatabaseConfig.java:88)
	at lms.cbt.system/com.lmssoft.config.DatabaseConfig.getOfflineSessionFactory(DatabaseConfig.java:30)
	at lms.cbt.system/com.lmssoft.service.DatabaseService.initialize(DatabaseService.java:47)
	at lms.cbt.system/com.lmssoft.LMSApplication.init(LMSApplication.java:58)
	at javafx.graphics@19.0.2.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:825)
	at javafx.graphics@19.0.2.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.hibernate.boot.registry.selector.spi.StrategySelectionException: Unable to resolve name [org.hibernate.dialect.SQLiteDialect] as strategy [org.hibernate.dialect.Dialect]
	at <EMAIL>/org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.selectStrategyImplementor(StrategySelectorImpl.java:154)
	at <EMAIL>/org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:236)
	at <EMAIL>/org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:189)
	at <EMAIL>/org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect(DialectFactoryImpl.java:123)
	at <EMAIL>/org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:86)
	at <EMAIL>/org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:274)
	at <EMAIL>/org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:34)
	at <EMAIL>/org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:119)
	at <EMAIL>/org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:264)
	... 17 common frames omitted
Caused by: org.hibernate.boot.registry.classloading.spi.ClassLoadingException: Unable to load class [org.hibernate.dialect.SQLiteDialect]
	at <EMAIL>/org.hibernate.boot.registry.classloading.internal.ClassLoaderServiceImpl.classForName(ClassLoaderServiceImpl.java:123)
	at <EMAIL>/org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.selectStrategyImplementor(StrategySelectorImpl.java:150)
	... 25 common frames omitted
Caused by: java.lang.ClassNotFoundException: Could not load requested class : org.hibernate.dialect.SQLiteDialect
	at <EMAIL>/org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:215)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:587)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:467)
	at <EMAIL>/org.hibernate.boot.registry.classloading.internal.ClassLoaderServiceImpl.classForName(ClassLoaderServiceImpl.java:120)
	... 26 common frames omitted
Caused by: java.lang.Throwable: null
	at <EMAIL>/org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:208)
	... 31 common frames omitted
	Suppressed: java.lang.ClassNotFoundException: org.hibernate.dialect.SQLiteDialect
		at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
		at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
		at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520)
		at <EMAIL>/org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:205)
		... 31 common frames omitted
	Suppressed: java.lang.ClassNotFoundException: org.hibernate.dialect.SQLiteDialect
		at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
		at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
		at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520)
		at <EMAIL>/org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:205)
		... 31 common frames omitted
	Suppressed: java.lang.ClassNotFoundException: org.hibernate.dialect.SQLiteDialect
		at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
		at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
		at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520)
		at <EMAIL>/org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:205)
		... 31 common frames omitted
2025-09-12 14:57:57.543 [JavaFX-Launcher] ERROR com.lmssoft.service.DatabaseService - Failed to initialize database service
java.lang.RuntimeException: Failed to create offline database session factory
	at lms.cbt.system/com.lmssoft.config.DatabaseConfig.createOfflineSessionFactory(DatabaseConfig.java:95)
	at lms.cbt.system/com.lmssoft.config.DatabaseConfig.getOfflineSessionFactory(DatabaseConfig.java:30)
	at lms.cbt.system/com.lmssoft.service.DatabaseService.initialize(DatabaseService.java:47)
	at lms.cbt.system/com.lmssoft.LMSApplication.init(LMSApplication.java:58)
	at javafx.graphics@19.0.2.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:825)
	at javafx.graphics@19.0.2.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment] due to: Unable to resolve name [org.hibernate.dialect.SQLiteDialect] as strategy [org.hibernate.dialect.Dialect]
	at <EMAIL>/org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:277)
	at <EMAIL>/org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:239)
	at <EMAIL>/org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:216)
	at <EMAIL>/org.hibernate.boot.model.relational.Database.<init>(Database.java:45)
	at <EMAIL>/org.hibernate.boot.internal.InFlightMetadataCollectorImpl.getDatabase(InFlightMetadataCollectorImpl.java:230)
	at <EMAIL>/org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:198)
	at <EMAIL>/org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:166)
	at <EMAIL>/org.hibernate.boot.model.process.spi.MetadataBuildingProcess.build(MetadataBuildingProcess.java:125)
	at <EMAIL>/org.hibernate.boot.internal.MetadataBuilderImpl.build(MetadataBuilderImpl.java:451)
	at <EMAIL>/org.hibernate.boot.internal.MetadataBuilderImpl.build(MetadataBuilderImpl.java:102)
	at <EMAIL>/org.hibernate.cfg.Configuration.buildSessionFactory(Configuration.java:910)
	at lms.cbt.system/com.lmssoft.config.DatabaseConfig.createOfflineSessionFactory(DatabaseConfig.java:88)
	... 6 common frames omitted
Caused by: org.hibernate.boot.registry.selector.spi.StrategySelectionException: Unable to resolve name [org.hibernate.dialect.SQLiteDialect] as strategy [org.hibernate.dialect.Dialect]
	at <EMAIL>/org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.selectStrategyImplementor(StrategySelectorImpl.java:154)
	at <EMAIL>/org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:236)
	at <EMAIL>/org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:189)
	at <EMAIL>/org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect(DialectFactoryImpl.java:123)
	at <EMAIL>/org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:86)
	at <EMAIL>/org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:274)
	at <EMAIL>/org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:34)
	at <EMAIL>/org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:119)
	at <EMAIL>/org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:264)
	... 17 common frames omitted
Caused by: org.hibernate.boot.registry.classloading.spi.ClassLoadingException: Unable to load class [org.hibernate.dialect.SQLiteDialect]
	at <EMAIL>/org.hibernate.boot.registry.classloading.internal.ClassLoaderServiceImpl.classForName(ClassLoaderServiceImpl.java:123)
	at <EMAIL>/org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.selectStrategyImplementor(StrategySelectorImpl.java:150)
	... 25 common frames omitted
Caused by: java.lang.ClassNotFoundException: Could not load requested class : org.hibernate.dialect.SQLiteDialect
	at <EMAIL>/org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:215)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:587)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:467)
	at <EMAIL>/org.hibernate.boot.registry.classloading.internal.ClassLoaderServiceImpl.classForName(ClassLoaderServiceImpl.java:120)
	... 26 common frames omitted
Caused by: java.lang.Throwable: null
	at <EMAIL>/org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:208)
	... 31 common frames omitted
	Suppressed: java.lang.ClassNotFoundException: org.hibernate.dialect.SQLiteDialect
		at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
		at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
		at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520)
		at <EMAIL>/org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:205)
		... 31 common frames omitted
	Suppressed: java.lang.ClassNotFoundException: org.hibernate.dialect.SQLiteDialect
		at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
		at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
		at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520)
		at <EMAIL>/org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:205)
		... 31 common frames omitted
	Suppressed: java.lang.ClassNotFoundException: org.hibernate.dialect.SQLiteDialect
		at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
		at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
		at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520)
		at <EMAIL>/org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:205)
		... 31 common frames omitted
2025-09-12 14:59:23.972 [main] INFO  com.lmssoft.config.AppConfig - Configuration loaded successfully
