package com.lmssoft;

import com.lmssoft.config.AppConfig;
import com.lmssoft.config.DatabaseConfig;
import com.lmssoft.controller.WelcomeController;
import com.lmssoft.service.AuthService;
import com.lmssoft.service.DatabaseService;
import com.lmssoft.service.DataInitializationService;
import com.lmssoft.service.SyncService;
import com.lmssoft.util.SceneManager;
import javafx.application.Application;
import javafx.application.Platform;
import javafx.scene.Scene;
import javafx.scene.image.Image;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.Objects;

/**
 * Main application class for LMS CBT System
 * Handles application lifecycle, initialization, and shutdown
 */
public class LMSApplication extends Application {
    
    private static final Logger logger = LoggerFactory.getLogger(LMSApplication.class);
    
    private DatabaseService dbService;
    private SyncService syncService;
    private AuthService authService;
    private SceneManager sceneManager;
    
    public static void main(String[] args) {
        // Set system properties for better JavaFX performance
        System.setProperty("javafx.animation.fullspeed", "true");
        System.setProperty("prism.lcdtext", "false");
        System.setProperty("prism.subpixeltext", "false");
        
        logger.info("Starting LMS CBT System...");
        launch(args);
    }
    
    @Override
    public void init() throws Exception {
        super.init();
        logger.info("Initializing application components...");
        
        // Create data directory if it doesn't exist
        createDataDirectory();
        
        // Initialize configuration
        AppConfig.initialize();
        
        // Initialize database
        dbService = DatabaseService.getInstance();
        dbService.initialize();
        
        // Initialize services
        authService = AuthService.getInstance();
        syncService = SyncService.getInstance();

        // Initialize default data
        DataInitializationService dataInitService = new DataInitializationService();
        if (!dataInitService.isSystemInitialized()) {
            logger.info("System not initialized, creating default data...");
            dataInitService.initializeDefaultData();
        }

        logger.info("Application components initialized successfully");
    }
    
    @Override
    public void start(Stage primaryStage) throws Exception {
        try {
            logger.info("Starting JavaFX application...");
            
            // Initialize scene manager
            sceneManager = SceneManager.getInstance();
            sceneManager.setPrimaryStage(primaryStage);
            
            // Configure primary stage
            configurePrimaryStage(primaryStage);
            
            // Load welcome screen
            sceneManager.showWelcomeScreen();

            // Apply modern theme
            sceneManager.applyTheme("modern");

            // Start background services
            startBackgroundServices();
            
            logger.info("Application started successfully");
            
        } catch (Exception e) {
            logger.error("Failed to start application", e);
            showErrorAndExit("Failed to start application: " + e.getMessage());
        }
    }
    
    @Override
    public void stop() throws Exception {
        logger.info("Shutting down application...");
        
        try {
            // Stop background services
            if (syncService != null) {
                syncService.shutdown();
            }
            
            // Close database connections
            if (dbService != null) {
                dbService.shutdown();
            }
            
            logger.info("Application shutdown completed");
            
        } catch (Exception e) {
            logger.error("Error during application shutdown", e);
        } finally {
            super.stop();
        }
    }
    
    private void createDataDirectory() {
        File dataDir = new File("data");
        if (!dataDir.exists()) {
            boolean created = dataDir.mkdirs();
            if (created) {
                logger.info("Created data directory: {}", dataDir.getAbsolutePath());
            } else {
                logger.warn("Failed to create data directory: {}", dataDir.getAbsolutePath());
            }
        }
        
        File logsDir = new File("logs");
        if (!logsDir.exists()) {
            boolean created = logsDir.mkdirs();
            if (created) {
                logger.info("Created logs directory: {}", logsDir.getAbsolutePath());
            }
        }
    }
    
    private void configurePrimaryStage(Stage primaryStage) {
        AppConfig config = AppConfig.getInstance();
        
        primaryStage.setTitle(config.getAppTitle());
        primaryStage.setWidth(config.getWindowWidth());
        primaryStage.setHeight(config.getWindowHeight());
        primaryStage.setMinWidth(config.getMinWindowWidth());
        primaryStage.setMinHeight(config.getMinWindowHeight());
        
        // Set application icon
        try {
            Image icon = new Image(Objects.requireNonNull(
                getClass().getResourceAsStream("/images/app-icon.png")));
            primaryStage.getIcons().add(icon);
        } catch (Exception e) {
            logger.warn("Could not load application icon", e);
        }
        
        // Handle close request
        primaryStage.setOnCloseRequest(event -> {
            event.consume();
            handleApplicationExit();
        });
        
        // Center on screen
        primaryStage.centerOnScreen();
    }
    
    private void startBackgroundServices() {
        // Start sync service if enabled
        AppConfig config = AppConfig.getInstance();
        if (config.isSyncEnabled()) {
            syncService.start();
            logger.info("Sync service started");
        }
    }
    
    private void handleApplicationExit() {
        logger.info("Application exit requested");
        
        // Show confirmation dialog if needed
        Platform.runLater(() -> {
            try {
                stop();
                Platform.exit();
                System.exit(0);
            } catch (Exception e) {
                logger.error("Error during application exit", e);
                System.exit(1);
            }
        });
    }
    
    private void showErrorAndExit(String message) {
        logger.error("Critical error: {}", message);
        Platform.runLater(() -> {
            // Show error dialog
            System.err.println("Critical Error: " + message);
            Platform.exit();
            System.exit(1);
        });
    }
    
    /**
     * Get the database service instance
     */
    public static DatabaseService getDatabaseService() {
        return DatabaseService.getInstance();
    }
    
    /**
     * Get the authentication service instance
     */
    public static AuthService getAuthService() {
        return AuthService.getInstance();
    }
    
    /**
     * Get the sync service instance
     */
    public static SyncService getSyncService() {
        return SyncService.getInstance();
    }
    
    /**
     * Get the scene manager instance
     */
    public static SceneManager getSceneManager() {
        return SceneManager.getInstance();
    }
}
