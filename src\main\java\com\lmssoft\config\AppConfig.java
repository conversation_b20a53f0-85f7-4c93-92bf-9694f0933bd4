package com.lmssoft.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.yaml.snakeyaml.Yaml;

import java.io.InputStream;
import java.util.Map;

/**
 * Application configuration manager
 * Loads and manages configuration from application.yml
 */
public class AppConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(AppConfig.class);
    private static AppConfig instance;
    
    private Map<String, Object> config;
    
    private AppConfig() {
        loadConfiguration();
    }
    
    public static void initialize() {
        if (instance == null) {
            instance = new AppConfig();
        }
    }
    
    public static AppConfig getInstance() {
        if (instance == null) {
            initialize();
        }
        return instance;
    }
    
    @SuppressWarnings("unchecked")
    private void loadConfiguration() {
        try {
            Yaml yaml = new Yaml();
            InputStream inputStream = getClass().getClassLoader()
                .getResourceAsStream("application.yml");
            
            if (inputStream == null) {
                throw new RuntimeException("application.yml not found in classpath");
            }
            
            config = yaml.load(inputStream);
            logger.info("Configuration loaded successfully");
            
        } catch (Exception e) {
            logger.error("Failed to load configuration", e);
            throw new RuntimeException("Failed to load application configuration", e);
        }
    }
    
    @SuppressWarnings("unchecked")
    private <T> T getConfigValue(String path, T defaultValue) {
        try {
            String[] keys = path.split("\\.");
            Object current = config;
            
            for (String key : keys) {
                if (current instanceof Map) {
                    current = ((Map<String, Object>) current).get(key);
                } else {
                    return defaultValue;
                }
            }
            
            return current != null ? (T) current : defaultValue;
            
        } catch (Exception e) {
            logger.warn("Failed to get config value for path: {}", path, e);
            return defaultValue;
        }
    }
    
    // App configuration
    public String getAppName() {
        return getConfigValue("app.name", "LMS CBT System");
    }
    
    public String getAppVersion() {
        return getConfigValue("app.version", "1.0.0");
    }
    
    public String getAppTitle() {
        return getConfigValue("app.title", "Learning Management System with CBT");
    }
    
    // Database configuration
    public String getOfflineDbDriver() {
        return getConfigValue("database.offline.driver", "org.sqlite.JDBC");
    }
    
    public String getOfflineDbUrl() {
        return getConfigValue("database.offline.url", "*******************************");
    }
    
    public String getOfflineDbDialect() {
        return getConfigValue("database.offline.dialect", "org.hibernate.dialect.SQLiteDialect");
    }
    
    public String getOnlineDbDriver() {
        return getConfigValue("database.online.driver", "com.mysql.cj.jdbc.Driver");
    }
    
    public String getOnlineDbUrl() {
        return getConfigValue("database.online.url", "***********************************");
    }
    
    public String getOnlineDbUsername() {
        return getConfigValue("database.online.username", "lms_user");
    }
    
    public String getOnlineDbPassword() {
        return getConfigValue("database.online.password", "lms_password");
    }
    
    public String getOnlineDbDialect() {
        return getConfigValue("database.online.dialect", "org.hibernate.dialect.MySQL8Dialect");
    }
    
    // Hibernate configuration
    public String getHibernateHbm2ddlAuto() {
        return getConfigValue("hibernate.hbm2ddl.auto", "update");
    }
    
    public boolean isHibernateShowSql() {
        return getConfigValue("hibernate.show_sql", false);
    }
    
    public boolean isHibernateFormatSql() {
        return getConfigValue("hibernate.format_sql", true);
    }
    
    public int getHibernateConnectionPoolSize() {
        return getConfigValue("hibernate.connection.pool_size", 10);
    }
    
    // Security configuration
    public String getJwtSecret() {
        return getConfigValue("security.jwt.secret", "lms-cbt-secret-key-2024");
    }
    
    public long getJwtExpiration() {
        return getConfigValue("security.jwt.expiration", 86400000L);
    }
    
    public int getPasswordMinLength() {
        return getConfigValue("security.password.min_length", 8);
    }
    
    public boolean isPasswordRequireSpecialChars() {
        return getConfigValue("security.password.require_special_chars", true);
    }
    
    public boolean isPasswordRequireNumbers() {
        return getConfigValue("security.password.require_numbers", true);
    }
    
    // UI configuration
    public String getUiTheme() {
        return getConfigValue("ui.theme", "modern");
    }
    
    public int getAnimationDuration() {
        return getConfigValue("ui.animation_duration", 300);
    }
    
    public double getWindowWidth() {
        return getConfigValue("ui.window.width", 1200.0);
    }
    
    public double getWindowHeight() {
        return getConfigValue("ui.window.height", 800.0);
    }
    
    public double getMinWindowWidth() {
        return getConfigValue("ui.window.min_width", 1000.0);
    }
    
    public double getMinWindowHeight() {
        return getConfigValue("ui.window.min_height", 600.0);
    }
    
    // Sync configuration
    public boolean isSyncEnabled() {
        return getConfigValue("sync.enabled", true);
    }
    
    public long getSyncInterval() {
        return getConfigValue("sync.interval", 300000L);
    }
    
    public int getSyncRetryAttempts() {
        return getConfigValue("sync.retry_attempts", 3);
    }
    
    public int getSyncTimeout() {
        return getConfigValue("sync.timeout", 30000);
    }
    
    // Feature flags
    public boolean isOfflineModeEnabled() {
        return getConfigValue("features.offline_mode", true);
    }
    
    public boolean isAutoSyncEnabled() {
        return getConfigValue("features.auto_sync", true);
    }
    
    public boolean isBackupEnabled() {
        return getConfigValue("features.backup_enabled", true);
    }
    
    public boolean isMultiLanguageEnabled() {
        return getConfigValue("features.multi_language", false);
    }
    
    public boolean isAccessibilityEnabled() {
        return getConfigValue("features.accessibility", true);
    }
}
