package com.lmssoft.config;

import org.hibernate.SessionFactory;
import org.hibernate.boot.registry.StandardServiceRegistryBuilder;
import org.hibernate.cfg.Configuration;
import org.hibernate.service.ServiceRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

/**
 * Database configuration and session factory management
 * Handles both offline (SQLite) and online (MySQL) database configurations
 */
public class DatabaseConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseConfig.class);
    
    private static SessionFactory offlineSessionFactory;
    private static SessionFactory onlineSessionFactory;
    
    /**
     * Get offline database session factory (SQLite)
     */
    public static SessionFactory getOfflineSessionFactory() {
        if (offlineSessionFactory == null) {
            synchronized (DatabaseConfig.class) {
                if (offlineSessionFactory == null) {
                    offlineSessionFactory = createOfflineSessionFactory();
                }
            }
        }
        return offlineSessionFactory;
    }
    
    /**
     * Get online database session factory (MySQL)
     */
    public static SessionFactory getOnlineSessionFactory() {
        if (onlineSessionFactory == null) {
            synchronized (DatabaseConfig.class) {
                if (onlineSessionFactory == null) {
                    onlineSessionFactory = createOnlineSessionFactory();
                }
            }
        }
        return onlineSessionFactory;
    }
    
    private static SessionFactory createOfflineSessionFactory() {
        try {
            logger.info("Creating offline database session factory...");
            
            AppConfig config = AppConfig.getInstance();
            Configuration configuration = new Configuration();
            
            // Database connection properties
            Properties properties = new Properties();
            properties.setProperty("hibernate.connection.driver_class", config.getOfflineDbDriver());
            properties.setProperty("hibernate.connection.url", config.getOfflineDbUrl());
            properties.setProperty("hibernate.dialect", config.getOfflineDbDialect());
            
            // Hibernate properties
            properties.setProperty("hibernate.hbm2ddl.auto", config.getHibernateHbm2ddlAuto());
            properties.setProperty("hibernate.show_sql", String.valueOf(config.isHibernateShowSql()));
            properties.setProperty("hibernate.format_sql", String.valueOf(config.isHibernateFormatSql()));
            properties.setProperty("hibernate.use_sql_comments", "true");
            
            // Connection pool settings
            properties.setProperty("hibernate.connection.pool_size", 
                String.valueOf(config.getHibernateConnectionPoolSize()));
            
            // SQLite specific settings
            properties.setProperty("hibernate.connection.autocommit", "false");
            properties.setProperty("hibernate.cache.use_second_level_cache", "false");
            properties.setProperty("hibernate.cache.use_query_cache", "false");
            
            configuration.setProperties(properties);
            
            // Add entity classes
            addEntityClasses(configuration);
            
            ServiceRegistry serviceRegistry = new StandardServiceRegistryBuilder()
                .applySettings(configuration.getProperties())
                .build();
            
            SessionFactory sessionFactory = configuration.buildSessionFactory(serviceRegistry);
            logger.info("Offline database session factory created successfully");
            
            return sessionFactory;
            
        } catch (Exception e) {
            logger.error("Failed to create offline database session factory", e);
            throw new RuntimeException("Failed to create offline database session factory", e);
        }
    }
    
    private static SessionFactory createOnlineSessionFactory() {
        try {
            logger.info("Creating online database session factory...");
            
            AppConfig config = AppConfig.getInstance();
            Configuration configuration = new Configuration();
            
            // Database connection properties
            Properties properties = new Properties();
            properties.setProperty("hibernate.connection.driver_class", config.getOnlineDbDriver());
            properties.setProperty("hibernate.connection.url", config.getOnlineDbUrl());
            properties.setProperty("hibernate.connection.username", config.getOnlineDbUsername());
            properties.setProperty("hibernate.connection.password", config.getOnlineDbPassword());
            properties.setProperty("hibernate.dialect", config.getOnlineDbDialect());
            
            // Hibernate properties
            properties.setProperty("hibernate.hbm2ddl.auto", config.getHibernateHbm2ddlAuto());
            properties.setProperty("hibernate.show_sql", String.valueOf(config.isHibernateShowSql()));
            properties.setProperty("hibernate.format_sql", String.valueOf(config.isHibernateFormatSql()));
            properties.setProperty("hibernate.use_sql_comments", "true");
            
            // Connection pool settings (HikariCP)
            properties.setProperty("hibernate.connection.provider_class", 
                "org.hibernate.hikaricp.internal.HikariCPConnectionProvider");
            properties.setProperty("hibernate.hikari.maximumPoolSize", "20");
            properties.setProperty("hibernate.hikari.minimumIdle", "5");
            properties.setProperty("hibernate.hikari.connectionTimeout", "30000");
            properties.setProperty("hibernate.hikari.idleTimeout", "600000");
            properties.setProperty("hibernate.hikari.maxLifetime", "1800000");
            
            // MySQL specific settings
            properties.setProperty("hibernate.connection.autocommit", "false");
            properties.setProperty("hibernate.cache.use_second_level_cache", "true");
            properties.setProperty("hibernate.cache.use_query_cache", "true");
            properties.setProperty("hibernate.cache.region.factory_class", 
                "org.hibernate.cache.jcache.JCacheRegionFactory");
            
            configuration.setProperties(properties);
            
            // Add entity classes
            addEntityClasses(configuration);
            
            ServiceRegistry serviceRegistry = new StandardServiceRegistryBuilder()
                .applySettings(configuration.getProperties())
                .build();
            
            SessionFactory sessionFactory = configuration.buildSessionFactory(serviceRegistry);
            logger.info("Online database session factory created successfully");
            
            return sessionFactory;
            
        } catch (Exception e) {
            logger.error("Failed to create online database session factory", e);
            throw new RuntimeException("Failed to create online database session factory", e);
        }
    }
    
    private static void addEntityClasses(Configuration configuration) {
        // Add all entity classes here
        configuration.addAnnotatedClass(com.lmssoft.model.User.class);
        configuration.addAnnotatedClass(com.lmssoft.model.Role.class);
        configuration.addAnnotatedClass(com.lmssoft.model.Course.class);
        configuration.addAnnotatedClass(com.lmssoft.model.Test.class);
        configuration.addAnnotatedClass(com.lmssoft.model.Question.class);
        configuration.addAnnotatedClass(com.lmssoft.model.Answer.class);
        configuration.addAnnotatedClass(com.lmssoft.model.TestResult.class);
        configuration.addAnnotatedClass(com.lmssoft.model.Assignment.class);
        configuration.addAnnotatedClass(com.lmssoft.model.Submission.class);
        configuration.addAnnotatedClass(com.lmssoft.model.Attendance.class);
        configuration.addAnnotatedClass(com.lmssoft.model.Grade.class);
        configuration.addAnnotatedClass(com.lmssoft.model.Resource.class);
        configuration.addAnnotatedClass(com.lmssoft.model.Notification.class);
        configuration.addAnnotatedClass(com.lmssoft.model.SyncRecord.class);
    }
    
    /**
     * Close all session factories
     */
    public static void shutdown() {
        try {
            if (offlineSessionFactory != null && !offlineSessionFactory.isClosed()) {
                offlineSessionFactory.close();
                logger.info("Offline database session factory closed");
            }
            
            if (onlineSessionFactory != null && !onlineSessionFactory.isClosed()) {
                onlineSessionFactory.close();
                logger.info("Online database session factory closed");
            }
            
        } catch (Exception e) {
            logger.error("Error closing database session factories", e);
        }
    }
    
    /**
     * Test database connectivity
     */
    public static boolean testOfflineConnection() {
        try {
            SessionFactory factory = getOfflineSessionFactory();
            factory.openSession().close();
            return true;
        } catch (Exception e) {
            logger.error("Offline database connection test failed", e);
            return false;
        }
    }
    
    /**
     * Test online database connectivity
     */
    public static boolean testOnlineConnection() {
        try {
            SessionFactory factory = getOnlineSessionFactory();
            factory.openSession().close();
            return true;
        } catch (Exception e) {
            logger.error("Online database connection test failed", e);
            return false;
        }
    }
}
