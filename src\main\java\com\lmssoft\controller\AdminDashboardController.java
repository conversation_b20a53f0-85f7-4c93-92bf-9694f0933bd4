package com.lmssoft.controller;

import com.lmssoft.service.AuthService;
import com.lmssoft.service.DatabaseService;
import com.lmssoft.service.SyncService;
import com.lmssoft.util.SceneManager;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ResourceBundle;

/**
 * Controller for the administrator dashboard
 * Provides system overview and administrative functions
 */
public class AdminDashboardController implements Initializable {
    
    private static final Logger logger = LoggerFactory.getLogger(AdminDashboardController.class);
    
    // Navigation buttons
    @FXML private Button systemStatusButton;
    @FXML private Button notificationsButton;
    @FXML private Button profileButton;
    @FXML private Button logoutButton;
    
    // Sidebar navigation
    @FXML private Button dashboardNavButton;
    @FXML private Button usersNavButton;
    @FXML private Button coursesNavButton;
    @FXML private Button testsNavButton;
    @FXML private Button reportsNavButton;
    @FXML private Button attendanceNavButton;
    @FXML private Button systemNavButton;
    @FXML private Button backupNavButton;
    
    // Dashboard content
    @FXML private Label welcomeLabel;
    @FXML private Label dateLabel;
    @FXML private Label totalUsersLabel;
    @FXML private Label activeCoursesLabel;
    @FXML private Label testsLabel;
    @FXML private Label systemHealthLabel;
    @FXML private Label uptimeLabel;
    @FXML private Label statusLabel;
    @FXML private Label connectionStatusLabel;
    @FXML private Label syncStatusLabel;
    @FXML private Label usersOnlineLabel;
    
    // Quick action buttons
    @FXML private Button createUserButton;
    @FXML private Button createCourseButton;
    @FXML private Button viewReportsButton;
    @FXML private Button systemBackupButton;
    
    private SceneManager sceneManager;
    private AuthService authService;
    private DatabaseService dbService;
    private SyncService syncService;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        logger.debug("Initializing AdminDashboardController");
        
        // Get service instances
        sceneManager = SceneManager.getInstance();
        authService = AuthService.getInstance();
        dbService = DatabaseService.getInstance();
        syncService = SyncService.getInstance();
        
        // Initialize UI
        initializeUI();
        
        // Load dashboard data
        loadDashboardData();
    }
    
    private void initializeUI() {
        // Set current date
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEEE, MMMM d, yyyy");
        dateLabel.setText("Today is " + now.format(formatter));
        
        // Set welcome message
        if (authService.getCurrentUser() != null) {
            String firstName = authService.getCurrentUser().getFirstName();
            welcomeLabel.setText("Welcome, " + firstName + "!");
        }
        
        // Set initial status
        statusLabel.setText("System Ready");
        updateSystemStatus();
        
        // Add CSS style classes
        setupButtonStyles();
    }
    
    private void setupButtonStyles() {
        // Navigation buttons
        systemStatusButton.getStyleClass().add("nav-button");
        notificationsButton.getStyleClass().add("nav-button");
        profileButton.getStyleClass().add("nav-button");
        logoutButton.getStyleClass().add("nav-button");
        
        // Sidebar buttons
        dashboardNavButton.getStyleClass().addAll("sidebar-item", "active");
        usersNavButton.getStyleClass().add("sidebar-item");
        coursesNavButton.getStyleClass().add("sidebar-item");
        testsNavButton.getStyleClass().add("sidebar-item");
        reportsNavButton.getStyleClass().add("sidebar-item");
        attendanceNavButton.getStyleClass().add("sidebar-item");
        systemNavButton.getStyleClass().add("sidebar-item");
        backupNavButton.getStyleClass().add("sidebar-item");
        
        // Action buttons
        createUserButton.getStyleClass().add("primary");
        createCourseButton.getStyleClass().add("secondary");
        viewReportsButton.getStyleClass().add("secondary");
        systemBackupButton.getStyleClass().add("secondary");
    }
    
    private void loadDashboardData() {
        // TODO: Load actual data from database
        // For now, using placeholder data
        
        Platform.runLater(() -> {
            totalUsersLabel.setText("1,247");
            activeCoursesLabel.setText("45");
            testsLabel.setText("156");
            systemHealthLabel.setText("Excellent");
            uptimeLabel.setText("99.9% uptime");
            usersOnlineLabel.setText("Users Online: 247");
        });
    }
    
    private void updateSystemStatus() {
        // Update connection status
        if (dbService.isOnlineMode()) {
            connectionStatusLabel.setText("Online");
            connectionStatusLabel.getStyleClass().removeAll("error", "warning");
            connectionStatusLabel.getStyleClass().add("success");
        } else {
            connectionStatusLabel.setText("Offline Mode");
            connectionStatusLabel.getStyleClass().removeAll("error", "success");
            connectionStatusLabel.getStyleClass().add("warning");
        }
        
        // Update sync status
        SyncService.SyncStatus syncStatus = syncService.getSyncStatus();
        if (syncStatus.isRunning) {
            syncStatusLabel.setText("Sync: Active");
            syncStatusLabel.getStyleClass().removeAll("error", "warning");
            syncStatusLabel.getStyleClass().add("success");
        } else {
            syncStatusLabel.setText("Sync: Stopped");
            syncStatusLabel.getStyleClass().removeAll("error", "success");
            syncStatusLabel.getStyleClass().add("warning");
        }
    }
    
    // Navigation handlers
    
    @FXML
    private void handleSystemStatus() {
        logger.debug("System status button clicked");
        
        // Show system status dialog
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("System Status");
        alert.setHeaderText("LMS CBT System Status");
        
        SyncService.SyncStatus syncStatus = syncService.getSyncStatus();
        String statusText = "System Information:\n\n" +
                           "Database: " + (dbService.isOnlineMode() ? "Online" : "Offline Mode") + "\n" +
                           "Sync Service: " + (syncStatus.isRunning ? "Running" : "Stopped") + "\n" +
                           "Pending Sync Records: " + syncStatus.pendingCount + "\n" +
                           "Failed Sync Records: " + syncStatus.failedCount + "\n" +
                           "Synced Records: " + syncStatus.syncedCount + "\n\n" +
                           "All core services are operational.";
        
        alert.setContentText(statusText);
        alert.showAndWait();
    }
    
    @FXML
    private void handleNotifications() {
        logger.debug("Notifications button clicked");
        showInfoAlert("Notifications", "Admin notifications feature will be implemented in a future version.");
    }
    
    @FXML
    private void handleProfile() {
        logger.debug("Profile button clicked");
        try {
            sceneManager.showUserProfile();
        } catch (Exception e) {
            logger.error("Failed to show user profile", e);
            showErrorAlert("Navigation Error", "Failed to open profile: " + e.getMessage());
        }
    }
    
    @FXML
    private void handleLogout() {
        logger.debug("Logout button clicked");
        
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("Logout");
        confirmAlert.setHeaderText("Confirm Logout");
        confirmAlert.setContentText("Are you sure you want to logout?");
        
        confirmAlert.showAndWait().ifPresent(response -> {
            if (response.getButtonData().isDefaultButton()) {
                try {
                    authService.logout();
                    sceneManager.showWelcomeScreen();
                } catch (Exception e) {
                    logger.error("Failed to logout", e);
                    showErrorAlert("Logout Error", "Failed to logout: " + e.getMessage());
                }
            }
        });
    }
    
    // Sidebar navigation handlers
    
    @FXML
    private void showDashboard() {
        logger.debug("Dashboard navigation clicked");
        updateActiveNavButton(dashboardNavButton);
    }
    
    @FXML
    private void showUsers() {
        logger.debug("Users navigation clicked");
        updateActiveNavButton(usersNavButton);
        showInfoAlert("User Management", "User management interface will be implemented in a future version.");
    }
    
    @FXML
    private void showCourses() {
        logger.debug("Courses navigation clicked");
        updateActiveNavButton(coursesNavButton);
        showInfoAlert("Course Management", "Course management interface will be implemented in a future version.");
    }
    
    @FXML
    private void showTests() {
        logger.debug("Tests navigation clicked");
        updateActiveNavButton(testsNavButton);
        showInfoAlert("Test Management", "Test management interface will be implemented in a future version.");
    }
    
    @FXML
    private void showReports() {
        logger.debug("Reports navigation clicked");
        updateActiveNavButton(reportsNavButton);
        showInfoAlert("Reports & Analytics", "Reports and analytics interface will be implemented in a future version.");
    }
    
    @FXML
    private void showAttendance() {
        logger.debug("Attendance navigation clicked");
        updateActiveNavButton(attendanceNavButton);
        showInfoAlert("Attendance", "Attendance management interface will be implemented in a future version.");
    }
    
    @FXML
    private void showSystem() {
        logger.debug("System navigation clicked");
        updateActiveNavButton(systemNavButton);
        try {
            sceneManager.showSettings();
        } catch (Exception e) {
            logger.error("Failed to show settings", e);
            showErrorAlert("Navigation Error", "Failed to open system settings: " + e.getMessage());
        }
    }
    
    @FXML
    private void showBackup() {
        logger.debug("Backup navigation clicked");
        updateActiveNavButton(backupNavButton);
        showInfoAlert("Backup & Restore", "Backup and restore interface will be implemented in a future version.");
    }
    
    // Quick action handlers
    
    @FXML
    private void handleCreateUser() {
        logger.debug("Create user button clicked");
        showInfoAlert("Create User", "User creation interface will be implemented in a future version.");
    }
    
    @FXML
    private void handleCreateCourse() {
        logger.debug("Create course button clicked");
        showInfoAlert("Create Course", "Course creation interface will be implemented in a future version.");
    }
    
    @FXML
    private void handleViewReports() {
        logger.debug("View reports button clicked");
        showReports();
    }
    
    @FXML
    private void handleSystemBackup() {
        logger.debug("System backup button clicked");
        
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("System Backup");
        confirmAlert.setHeaderText("Confirm System Backup");
        confirmAlert.setContentText("This will create a backup of the entire system database. Continue?");
        
        confirmAlert.showAndWait().ifPresent(response -> {
            if (response.getButtonData().isDefaultButton()) {
                showInfoAlert("System Backup", "System backup functionality will be implemented in a future version.");
            }
        });
    }
    
    // Helper methods
    
    private void updateActiveNavButton(Button activeButton) {
        // Remove active class from all nav buttons
        dashboardNavButton.getStyleClass().remove("active");
        usersNavButton.getStyleClass().remove("active");
        coursesNavButton.getStyleClass().remove("active");
        testsNavButton.getStyleClass().remove("active");
        reportsNavButton.getStyleClass().remove("active");
        attendanceNavButton.getStyleClass().remove("active");
        systemNavButton.getStyleClass().remove("active");
        backupNavButton.getStyleClass().remove("active");
        
        // Add active class to clicked button
        activeButton.getStyleClass().add("active");
    }
    
    private void showErrorAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setHeaderText("Error");
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showInfoAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText("Information");
        alert.setContentText(message);
        alert.showAndWait();
    }
}
