package com.lmssoft.controller;

import com.lmssoft.service.AuthService;
import com.lmssoft.service.DatabaseService;
import com.lmssoft.util.SceneManager;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.util.ResourceBundle;

/**
 * Controller for the login screen
 * Handles user authentication and navigation
 */
public class LoginController implements Initializable {
    
    private static final Logger logger = LoggerFactory.getLogger(LoginController.class);
    
    @FXML private TextField usernameField;
    @FXML private PasswordField passwordField;
    @FXML private CheckBox rememberMeCheckBox;
    @FXML private Hyperlink forgotPasswordLink;
    @FXML private Hyperlink registerLink;
    @FXML private Button loginButton;
    @FXML private Button backButton;
    @FXML private Button helpButton;
    @FXML private Label usernameErrorLabel;
    @FXML private Label passwordErrorLabel;
    @FXML private Label errorMessageLabel;
    @FXML private Label systemStatusLabel;
    @FXML private ProgressIndicator loadingIndicator;
    
    private SceneManager sceneManager;
    private AuthService authService;
    private DatabaseService dbService;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        logger.debug("Initializing LoginController");
        
        // Get service instances
        sceneManager = SceneManager.getInstance();
        authService = AuthService.getInstance();
        dbService = DatabaseService.getInstance();
        
        // Initialize UI
        initializeUI();
        
        // Set up event handlers
        setupEventHandlers();
        
        // Update system status
        updateSystemStatus();
    }
    
    private void initializeUI() {
        // Clear any previous error messages
        clearErrorMessages();
        
        // Set focus to username field
        Platform.runLater(() -> usernameField.requestFocus());
        
        // Add CSS style classes
        loginButton.getStyleClass().add("primary");
        backButton.getStyleClass().add("nav-button");
        helpButton.getStyleClass().add("nav-button");
    }
    
    private void setupEventHandlers() {
        // Enter key handling
        usernameField.setOnAction(e -> passwordField.requestFocus());
        passwordField.setOnAction(e -> handleLogin());
        
        // Real-time validation
        usernameField.textProperty().addListener((obs, oldText, newText) -> {
            if (!newText.trim().isEmpty()) {
                clearFieldError(usernameField, usernameErrorLabel);
            }
        });
        
        passwordField.textProperty().addListener((obs, oldText, newText) -> {
            if (!newText.trim().isEmpty()) {
                clearFieldError(passwordField, passwordErrorLabel);
            }
        });
    }
    
    private void updateSystemStatus() {
        if (dbService.isOnlineMode()) {
            systemStatusLabel.setText("System Status: Online");
            systemStatusLabel.getStyleClass().removeAll("error", "warning");
            systemStatusLabel.getStyleClass().add("success");
        } else {
            systemStatusLabel.setText("System Status: Offline Mode");
            systemStatusLabel.getStyleClass().removeAll("error", "success");
            systemStatusLabel.getStyleClass().add("warning");
        }
    }
    
    @FXML
    private void handleLogin() {
        logger.debug("Login attempt started");
        
        // Clear previous error messages
        clearErrorMessages();
        
        // Get input values
        String usernameOrEmail = usernameField.getText().trim();
        String password = passwordField.getText();
        
        // Validate input
        if (!validateInput(usernameOrEmail, password)) {
            return;
        }
        
        // Disable UI during login
        setUIEnabled(false);
        showLoading(true);
        
        // Perform login in background thread
        Task<AuthService.AuthResult> loginTask = new Task<AuthService.AuthResult>() {
            @Override
            protected AuthService.AuthResult call() throws Exception {
                return authService.authenticate(usernameOrEmail, password);
            }
            
            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    AuthService.AuthResult result = getValue();
                    handleLoginResult(result);
                    setUIEnabled(true);
                    showLoading(false);
                });
            }
            
            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    logger.error("Login task failed", getException());
                    showErrorMessage("Login failed due to system error. Please try again.");
                    setUIEnabled(true);
                    showLoading(false);
                });
            }
        };
        
        // Run login task
        Thread loginThread = new Thread(loginTask);
        loginThread.setDaemon(true);
        loginThread.start();
    }
    
    private boolean validateInput(String usernameOrEmail, String password) {
        boolean isValid = true;
        
        // Validate username/email
        if (usernameOrEmail.isEmpty()) {
            showFieldError(usernameField, usernameErrorLabel, "Username or email is required");
            isValid = false;
        } else if (usernameOrEmail.length() < 3) {
            showFieldError(usernameField, usernameErrorLabel, "Username must be at least 3 characters");
            isValid = false;
        }
        
        // Validate password
        if (password.isEmpty()) {
            showFieldError(passwordField, passwordErrorLabel, "Password is required");
            isValid = false;
        } else if (password.length() < 6) {
            showFieldError(passwordField, passwordErrorLabel, "Password must be at least 6 characters");
            isValid = false;
        }
        
        return isValid;
    }
    
    private void handleLoginResult(AuthService.AuthResult result) {
        if (result.isSuccess()) {
            logger.info("Login successful for user: {}", result.getUser().getUsername());
            
            // Show success message briefly
            showSuccessMessage("Login successful! Redirecting...");
            
            // Navigate to appropriate dashboard based on user role
            Platform.runLater(() -> {
                try {
                    String userRole = result.getUser().getRole().getName();
                    sceneManager.showDashboard(userRole);
                } catch (Exception e) {
                    logger.error("Failed to navigate to dashboard", e);
                    showErrorMessage("Login successful but failed to load dashboard. Please try again.");
                }
            });
            
        } else {
            logger.warn("Login failed: {}", result.getMessage());
            showErrorMessage(result.getMessage());
            
            // Clear password field on failed login
            passwordField.clear();
            passwordField.requestFocus();
        }
    }
    
    @FXML
    private void handleForgotPassword() {
        logger.debug("Forgot password clicked");
        
        // Show forgot password dialog
        TextInputDialog dialog = new TextInputDialog();
        dialog.setTitle("Forgot Password");
        dialog.setHeaderText("Password Reset");
        dialog.setContentText("Enter your email address:");
        
        dialog.showAndWait().ifPresent(email -> {
            if (email.trim().isEmpty()) {
                showErrorAlert("Error", "Email address is required");
                return;
            }
            
            // TODO: Implement password reset functionality
            showInfoAlert("Password Reset", 
                "Password reset functionality will be implemented in a future version.\n\n" +
                "For now, please contact your system administrator to reset your password.");
        });
    }
    
    @FXML
    private void handleRegister() {
        logger.debug("Register link clicked");
        
        // TODO: Navigate to registration screen
        showInfoAlert("Registration", 
            "User registration functionality will be implemented in a future version.\n\n" +
            "For now, please contact your system administrator to create an account.");
    }
    
    @FXML
    private void handleBack() {
        logger.debug("Back button clicked");
        try {
            sceneManager.showWelcomeScreen();
        } catch (Exception e) {
            logger.error("Failed to navigate back to welcome screen", e);
            showErrorAlert("Navigation Error", "Failed to go back: " + e.getMessage());
        }
    }
    
    @FXML
    private void handleHelp() {
        logger.debug("Help button clicked");
        
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Login Help");
        alert.setHeaderText("How to Sign In");
        alert.setContentText("To sign in to the LMS CBT System:\n\n" +
                             "1. Enter your username or email address\n" +
                             "2. Enter your password\n" +
                             "3. Click 'Sign In' or press Enter\n\n" +
                             "Default Admin Credentials:\n" +
                             "Username: admin\n" +
                             "Password: Admin@123\n\n" +
                             "If you're having trouble signing in:\n" +
                             "• Check your username and password\n" +
                             "• Ensure Caps Lock is off\n" +
                             "• Contact your system administrator\n\n" +
                             "The system works in both online and offline modes.");
        
        alert.showAndWait();
    }
    
    // UI Helper Methods
    
    private void setUIEnabled(boolean enabled) {
        usernameField.setDisable(!enabled);
        passwordField.setDisable(!enabled);
        rememberMeCheckBox.setDisable(!enabled);
        loginButton.setDisable(!enabled);
        forgotPasswordLink.setDisable(!enabled);
        registerLink.setDisable(!enabled);
    }
    
    private void showLoading(boolean show) {
        loadingIndicator.setVisible(show);
        if (show) {
            loginButton.setText("Signing In...");
        } else {
            loginButton.setText("Sign In");
        }
    }
    
    private void clearErrorMessages() {
        errorMessageLabel.setVisible(false);
        usernameErrorLabel.setVisible(false);
        passwordErrorLabel.setVisible(false);
        
        // Remove error styling
        usernameField.getStyleClass().remove("error");
        passwordField.getStyleClass().remove("error");
    }
    
    private void showFieldError(Control field, Label errorLabel, String message) {
        field.getStyleClass().add("error");
        errorLabel.setText(message);
        errorLabel.setVisible(true);
    }
    
    private void clearFieldError(Control field, Label errorLabel) {
        field.getStyleClass().remove("error");
        errorLabel.setVisible(false);
    }
    
    private void showErrorMessage(String message) {
        errorMessageLabel.setText(message);
        errorMessageLabel.setVisible(true);
        errorMessageLabel.getStyleClass().removeAll("success");
        errorMessageLabel.getStyleClass().add("error");
    }
    
    private void showSuccessMessage(String message) {
        errorMessageLabel.setText(message);
        errorMessageLabel.setVisible(true);
        errorMessageLabel.getStyleClass().removeAll("error");
        errorMessageLabel.getStyleClass().add("success");
    }
    
    private void showErrorAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setHeaderText("Error");
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showInfoAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText("Information");
        alert.setContentText(message);
        alert.showAndWait();
    }
}
