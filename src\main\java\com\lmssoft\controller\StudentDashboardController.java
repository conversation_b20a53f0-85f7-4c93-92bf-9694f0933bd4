package com.lmssoft.controller;

import com.lmssoft.service.AuthService;
import com.lmssoft.util.SceneManager;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ResourceBundle;

/**
 * Controller for the student dashboard
 * Provides overview of student activities and quick access to features
 */
public class StudentDashboardController implements Initializable {
    
    private static final Logger logger = LoggerFactory.getLogger(StudentDashboardController.class);
    
    // Navigation buttons
    @FXML private Button notificationsButton;
    @FXML private Button profileButton;
    @FXML private Button logoutButton;
    
    // Sidebar navigation
    @FXML private Button dashboardNavButton;
    @FXML private Button coursesNavButton;
    @FXML private Button testsNavButton;
    @FXML private Button assignmentsNavButton;
    @FXML private Button gradesNavButton;
    @FXML private Button attendanceNavButton;
    @FXML private Button resourcesNavButton;
    
    // Dashboard content
    @FXML private Label welcomeLabel;
    @FXML private Label dateLabel;
    @FXML private Label activeCoursesLabel;
    @FXML private Label pendingTestsLabel;
    @FXML private Label assignmentsDueLabel;
    @FXML private Label overallGradeLabel;
    @FXML private Label statusLabel;
    @FXML private Label connectionStatusLabel;
    
    // Quick action buttons
    @FXML private Button takeTestButton;
    @FXML private Button viewCoursesButton;
    @FXML private Button checkGradesButton;
    
    private SceneManager sceneManager;
    private AuthService authService;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        logger.debug("Initializing StudentDashboardController");
        
        // Get service instances
        sceneManager = SceneManager.getInstance();
        authService = AuthService.getInstance();
        
        // Initialize UI
        initializeUI();
        
        // Load dashboard data
        loadDashboardData();
    }
    
    private void initializeUI() {
        // Set current date
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEEE, MMMM d, yyyy");
        dateLabel.setText("Today is " + now.format(formatter));
        
        // Set welcome message
        if (authService.getCurrentUser() != null) {
            String firstName = authService.getCurrentUser().getFirstName();
            welcomeLabel.setText("Welcome back, " + firstName + "!");
        }
        
        // Set initial status
        statusLabel.setText("Ready");
        connectionStatusLabel.setText("Online");
        
        // Add CSS style classes
        setupButtonStyles();
    }
    
    private void setupButtonStyles() {
        // Navigation buttons
        notificationsButton.getStyleClass().add("nav-button");
        profileButton.getStyleClass().add("nav-button");
        logoutButton.getStyleClass().add("nav-button");
        
        // Sidebar buttons
        dashboardNavButton.getStyleClass().addAll("sidebar-item", "active");
        coursesNavButton.getStyleClass().add("sidebar-item");
        testsNavButton.getStyleClass().add("sidebar-item");
        assignmentsNavButton.getStyleClass().add("sidebar-item");
        gradesNavButton.getStyleClass().add("sidebar-item");
        attendanceNavButton.getStyleClass().add("sidebar-item");
        resourcesNavButton.getStyleClass().add("sidebar-item");
        
        // Action buttons
        takeTestButton.getStyleClass().add("primary");
        viewCoursesButton.getStyleClass().add("secondary");
        checkGradesButton.getStyleClass().add("secondary");
    }
    
    private void loadDashboardData() {
        // TODO: Load actual data from database
        // For now, using placeholder data
        
        Platform.runLater(() -> {
            activeCoursesLabel.setText("5");
            pendingTestsLabel.setText("3");
            assignmentsDueLabel.setText("2");
            overallGradeLabel.setText("85%");
        });
    }
    
    // Navigation handlers
    
    @FXML
    private void handleNotifications() {
        logger.debug("Notifications button clicked");
        showInfoAlert("Notifications", "Notifications feature will be implemented in a future version.");
    }
    
    @FXML
    private void handleProfile() {
        logger.debug("Profile button clicked");
        try {
            sceneManager.showUserProfile();
        } catch (Exception e) {
            logger.error("Failed to show user profile", e);
            showErrorAlert("Navigation Error", "Failed to open profile: " + e.getMessage());
        }
    }
    
    @FXML
    private void handleLogout() {
        logger.debug("Logout button clicked");
        
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("Logout");
        confirmAlert.setHeaderText("Confirm Logout");
        confirmAlert.setContentText("Are you sure you want to logout?");
        
        confirmAlert.showAndWait().ifPresent(response -> {
            if (response.getButtonData().isDefaultButton()) {
                try {
                    authService.logout();
                    sceneManager.showWelcomeScreen();
                } catch (Exception e) {
                    logger.error("Failed to logout", e);
                    showErrorAlert("Logout Error", "Failed to logout: " + e.getMessage());
                }
            }
        });
    }
    
    // Sidebar navigation handlers
    
    @FXML
    private void showDashboard() {
        logger.debug("Dashboard navigation clicked");
        // Already on dashboard - just update active state
        updateActiveNavButton(dashboardNavButton);
    }
    
    @FXML
    private void showCourses() {
        logger.debug("Courses navigation clicked");
        updateActiveNavButton(coursesNavButton);
        showInfoAlert("Courses", "Course management feature will be implemented in a future version.");
    }
    
    @FXML
    private void showTests() {
        logger.debug("Tests navigation clicked");
        updateActiveNavButton(testsNavButton);
        showInfoAlert("Tests", "Test management feature will be implemented in a future version.");
    }
    
    @FXML
    private void showAssignments() {
        logger.debug("Assignments navigation clicked");
        updateActiveNavButton(assignmentsNavButton);
        showInfoAlert("Assignments", "Assignment management feature will be implemented in a future version.");
    }
    
    @FXML
    private void showGrades() {
        logger.debug("Grades navigation clicked");
        updateActiveNavButton(gradesNavButton);
        showInfoAlert("Grades", "Grade viewing feature will be implemented in a future version.");
    }
    
    @FXML
    private void showAttendance() {
        logger.debug("Attendance navigation clicked");
        updateActiveNavButton(attendanceNavButton);
        showInfoAlert("Attendance", "Attendance tracking feature will be implemented in a future version.");
    }
    
    @FXML
    private void showResources() {
        logger.debug("Resources navigation clicked");
        updateActiveNavButton(resourcesNavButton);
        showInfoAlert("Resources", "Resource management feature will be implemented in a future version.");
    }
    
    // Quick action handlers
    
    @FXML
    private void handleTakeTest() {
        logger.debug("Take test button clicked");
        showInfoAlert("Take Test", "Test taking interface will be implemented in a future version.");
    }
    
    @FXML
    private void handleViewCourses() {
        logger.debug("View courses button clicked");
        showCourses();
    }
    
    @FXML
    private void handleCheckGrades() {
        logger.debug("Check grades button clicked");
        showGrades();
    }
    
    // Helper methods
    
    private void updateActiveNavButton(Button activeButton) {
        // Remove active class from all nav buttons
        dashboardNavButton.getStyleClass().remove("active");
        coursesNavButton.getStyleClass().remove("active");
        testsNavButton.getStyleClass().remove("active");
        assignmentsNavButton.getStyleClass().remove("active");
        gradesNavButton.getStyleClass().remove("active");
        attendanceNavButton.getStyleClass().remove("active");
        resourcesNavButton.getStyleClass().remove("active");
        
        // Add active class to clicked button
        activeButton.getStyleClass().add("active");
    }
    
    private void showErrorAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setHeaderText("Error");
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showInfoAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText("Information");
        alert.setContentText(message);
        alert.showAndWait();
    }
}
