package com.lmssoft.controller;

import com.lmssoft.service.AuthService;
import com.lmssoft.util.SceneManager;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.util.ResourceBundle;

/**
 * Controller for the teacher dashboard (placeholder)
 */
public class TeacherDashboardController implements Initializable {
    
    private static final Logger logger = LoggerFactory.getLogger(TeacherDashboardController.class);
    
    @FXML private Button backButton;
    @FXML private Button logoutButton;
    
    private SceneManager sceneManager;
    private AuthService authService;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        logger.debug("Initializing TeacherDashboardController");
        
        sceneManager = SceneManager.getInstance();
        authService = AuthService.getInstance();
        
        // Add CSS style classes
        backButton.getStyleClass().add("secondary");
        logoutButton.getStyleClass().add("primary");
    }
    
    @FXML
    private void handleBack() {
        logger.debug("Back button clicked");
        try {
            sceneManager.showLoginScreen();
        } catch (Exception e) {
            logger.error("Failed to navigate back to login", e);
        }
    }
    
    @FXML
    private void handleLogout() {
        logger.debug("Logout button clicked");
        
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("Logout");
        confirmAlert.setHeaderText("Confirm Logout");
        confirmAlert.setContentText("Are you sure you want to logout?");
        
        confirmAlert.showAndWait().ifPresent(response -> {
            if (response.getButtonData().isDefaultButton()) {
                try {
                    authService.logout();
                    sceneManager.showWelcomeScreen();
                } catch (Exception e) {
                    logger.error("Failed to logout", e);
                }
            }
        });
    }
}
