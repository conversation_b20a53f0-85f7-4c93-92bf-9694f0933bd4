package com.lmssoft.controller;

import com.lmssoft.LMSApplication;
import com.lmssoft.config.AppConfig;
import com.lmssoft.service.DatabaseService;
import com.lmssoft.util.SceneManager;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.ProgressIndicator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.util.ResourceBundle;

/**
 * Controller for the welcome screen
 * Handles initial user interactions and navigation
 */
public class WelcomeController implements Initializable {
    
    private static final Logger logger = LoggerFactory.getLogger(WelcomeController.class);
    
    @FXML private Button loginButton;
    @FXML private Button registerButton;
    @FXML private Button guestButton;
    @FXML private Button demoButton;
    @FXML private Button settingsButton;
    @FXML private Button helpButton;
    @FXML private Button aboutButton;
    @FXML private Label statusLabel;
    @FXML private Label versionLabel;
    @FXML private ProgressIndicator loadingIndicator;
    
    private SceneManager sceneManager;
    private DatabaseService dbService;
    private AppConfig config;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        logger.debug("Initializing WelcomeController");
        
        // Get service instances
        sceneManager = SceneManager.getInstance();
        dbService = DatabaseService.getInstance();
        config = AppConfig.getInstance();
        
        // Initialize UI
        initializeUI();
        
        // Check system status
        checkSystemStatus();
    }
    
    private void initializeUI() {
        // Set version label
        versionLabel.setText("Version " + config.getAppVersion());
        
        // Set initial status
        updateSystemStatus();
        
        // Add hover effects and styling
        setupButtonEffects();
    }
    
    private void setupButtonEffects() {
        // Add CSS style classes
        loginButton.getStyleClass().add("primary");
        registerButton.getStyleClass().add("secondary");
        guestButton.getStyleClass().add("secondary");
        demoButton.getStyleClass().add("secondary");
        
        // Navigation buttons
        settingsButton.getStyleClass().add("nav-button");
        helpButton.getStyleClass().add("nav-button");
        aboutButton.getStyleClass().add("nav-button");
    }
    
    private void checkSystemStatus() {
        // Run system check in background
        Task<Boolean> statusTask = new Task<Boolean>() {
            @Override
            protected Boolean call() throws Exception {
                // Test database connectivity
                return dbService.testConnection();
            }
            
            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    boolean isOnline = getValue();
                    updateSystemStatus(isOnline);
                    loadingIndicator.setVisible(false);
                });
            }
            
            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    updateSystemStatus(false);
                    loadingIndicator.setVisible(false);
                });
            }
        };
        
        // Show loading indicator
        loadingIndicator.setVisible(true);
        
        // Run task
        Thread statusThread = new Thread(statusTask);
        statusThread.setDaemon(true);
        statusThread.start();
    }
    
    private void updateSystemStatus() {
        updateSystemStatus(dbService.isOnlineMode());
    }
    
    private void updateSystemStatus(boolean isOnline) {
        if (isOnline) {
            statusLabel.setText("System Status: Online");
            statusLabel.getStyleClass().removeAll("error");
            statusLabel.getStyleClass().add("success");
        } else {
            statusLabel.setText("System Status: Offline Mode");
            statusLabel.getStyleClass().removeAll("success");
            statusLabel.getStyleClass().add("warning");
        }
    }
    
    @FXML
    private void handleLogin() {
        logger.debug("Login button clicked");
        try {
            sceneManager.showLoginScreen();
        } catch (Exception e) {
            logger.error("Failed to show login screen", e);
            showErrorAlert("Navigation Error", "Failed to open login screen: " + e.getMessage());
        }
    }
    
    @FXML
    private void handleRegister() {
        logger.debug("Register button clicked");
        try {
            // For now, redirect to login screen with register mode
            // TODO: Create separate registration screen
            sceneManager.showLoginScreen();
        } catch (Exception e) {
            logger.error("Failed to show registration screen", e);
            showErrorAlert("Navigation Error", "Failed to open registration screen: " + e.getMessage());
        }
    }
    
    @FXML
    private void handleGuestAccess() {
        logger.debug("Guest access button clicked");
        
        // Show info dialog about guest access
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Guest Access");
        alert.setHeaderText("Guest Access Information");
        alert.setContentText("Guest access allows you to explore the system with limited functionality. " +
                             "Some features may not be available without proper authentication.");
        
        alert.showAndWait().ifPresent(response -> {
            try {
                // TODO: Implement guest access logic
                // For now, show a demo dashboard
                showInfoAlert("Guest Access", "Guest access feature will be implemented in a future version.");
            } catch (Exception e) {
                logger.error("Failed to handle guest access", e);
                showErrorAlert("Error", "Failed to enable guest access: " + e.getMessage());
            }
        });
    }
    
    @FXML
    private void handleDemo() {
        logger.debug("Demo button clicked");
        
        // Show demo information
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("System Demo");
        alert.setHeaderText("LMS CBT System Demo");
        alert.setContentText("This demo showcases the key features of our Learning Management System:\n\n" +
                             "• Course Management\n" +
                             "• Computer-Based Testing\n" +
                             "• Student Progress Tracking\n" +
                             "• Attendance Management\n" +
                             "• Analytics and Reporting\n\n" +
                             "Please login with your credentials to access the full system.");
        
        alert.showAndWait();
    }
    
    @FXML
    private void handleSettings() {
        logger.debug("Settings button clicked");
        try {
            sceneManager.showSettings();
        } catch (Exception e) {
            logger.error("Failed to show settings", e);
            showErrorAlert("Navigation Error", "Failed to open settings: " + e.getMessage());
        }
    }
    
    @FXML
    private void handleHelp() {
        logger.debug("Help button clicked");
        
        // Show help dialog
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Help");
        alert.setHeaderText("LMS CBT System Help");
        alert.setContentText("Welcome to the LMS CBT System!\n\n" +
                             "Getting Started:\n" +
                             "1. Click 'Login' if you have an account\n" +
                             "2. Click 'Register' to create a new account\n" +
                             "3. Use 'Guest Access' for limited exploration\n\n" +
                             "For technical support, please contact your system administrator.\n\n" +
                             "Default Admin Credentials:\n" +
                             "Username: admin\n" +
                             "Password: Admin@123");
        
        alert.showAndWait();
    }
    
    @FXML
    private void handleAbout() {
        logger.debug("About button clicked");
        
        // Show about dialog
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("About");
        alert.setHeaderText("LMS CBT System");
        alert.setContentText("Learning Management System with Computer-Based Testing\n\n" +
                             "Version: " + config.getAppVersion() + "\n" +
                             "Built with JavaFX and Hibernate\n\n" +
                             "Features:\n" +
                             "• Modern, responsive user interface\n" +
                             "• Comprehensive course management\n" +
                             "• Advanced CBT capabilities\n" +
                             "• Offline/Online synchronization\n" +
                             "• Role-based access control\n" +
                             "• Analytics and reporting\n\n" +
                             "© 2024 LMS CBT System. All rights reserved.");
        
        alert.showAndWait();
    }
    
    private void showErrorAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setHeaderText("Error");
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showInfoAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText("Information");
        alert.setContentText(message);
        alert.showAndWait();
    }
}
