package com.lmssoft.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * Answer entity for questions in CBT system
 * Represents answer options for questions
 */
@Entity
@Table(name = "answers", indexes = {
    @Index(name = "idx_answer_order", columnList = "answer_order"),
    @Index(name = "idx_answer_correct", columnList = "is_correct")
})
public class Answer extends BaseEntity {
    
    @NotBlank(message = "Answer text is required")
    @Size(max = 1000, message = "Answer text must not exceed 1000 characters")
    @Column(name = "answer_text", nullable = false, length = 1000)
    private String answerText;
    
    @Column(name = "is_correct", nullable = false)
    private Boolean isCorrect = false;
    
    @Column(name = "answer_order")
    private Integer answerOrder;
    
    @Size(max = 500, message = "Explanation must not exceed 500 characters")
    @Column(name = "explanation", length = 500)
    private String explanation;
    
    @Size(max = 255, message = "Image URL must not exceed 255 characters")
    @Column(name = "image_url", length = 255)
    private String imageUrl;
    
    @Column(name = "points")
    private Double points = 0.0;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "question_id", nullable = false)
    private Question question;
    
    // Constructors
    public Answer() {}
    
    public Answer(String answerText, Boolean isCorrect, Question question) {
        this.answerText = answerText;
        this.isCorrect = isCorrect;
        this.question = question;
    }
    
    // Getters and Setters
    public String getAnswerText() {
        return answerText;
    }
    
    public void setAnswerText(String answerText) {
        this.answerText = answerText;
    }
    
    public Boolean getIsCorrect() {
        return isCorrect;
    }
    
    public void setIsCorrect(Boolean isCorrect) {
        this.isCorrect = isCorrect;
    }
    
    public Integer getAnswerOrder() {
        return answerOrder;
    }
    
    public void setAnswerOrder(Integer answerOrder) {
        this.answerOrder = answerOrder;
    }
    
    public String getExplanation() {
        return explanation;
    }
    
    public void setExplanation(String explanation) {
        this.explanation = explanation;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public Double getPoints() {
        return points;
    }
    
    public void setPoints(Double points) {
        this.points = points;
    }
    
    public Question getQuestion() {
        return question;
    }
    
    public void setQuestion(Question question) {
        this.question = question;
    }
    
    @Override
    public String toString() {
        return "Answer{" +
                "id=" + getId() +
                ", answerText='" + (answerText.length() > 30 ? 
                    answerText.substring(0, 30) + "..." : answerText) + '\'' +
                ", isCorrect=" + isCorrect +
                ", answerOrder=" + answerOrder +
                ", points=" + points +
                '}';
    }
}
