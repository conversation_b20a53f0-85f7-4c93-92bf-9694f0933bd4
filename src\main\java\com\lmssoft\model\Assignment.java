package com.lmssoft.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * Assignment entity for LMS system
 * Represents assignments that can be given to students
 */
@Entity
@Table(name = "assignments", indexes = {
    @Index(name = "idx_assignment_due_date", columnList = "due_date"),
    @Index(name = "idx_assignment_status", columnList = "status")
})
public class Assignment extends BaseEntity {
    
    @NotBlank(message = "Assignment title is required")
    @Size(max = 200, message = "Assignment title must not exceed 200 characters")
    @Column(name = "title", nullable = false, length = 200)
    private String title;
    
    @Size(max = 2000, message = "Description must not exceed 2000 characters")
    @Column(name = "description", length = 2000)
    private String description;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "course_id", nullable = false)
    private Course course;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "teacher_id", nullable = false)
    private User teacher;
    
    @Column(name = "due_date")
    private LocalDateTime dueDate;
    
    @Column(name = "max_points", nullable = false)
    private Double maxPoints = 100.0;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private AssignmentStatus status = AssignmentStatus.DRAFT;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "submission_type", nullable = false, length = 20)
    private SubmissionType submissionType = SubmissionType.FILE_UPLOAD;
    
    @Column(name = "allow_late_submission", nullable = false)
    private Boolean allowLateSubmission = false;
    
    @Column(name = "late_penalty_percentage")
    private Double latePenaltyPercentage = 0.0;
    
    @Column(name = "max_file_size_mb")
    private Integer maxFileSizeMb = 10;
    
    @Size(max = 500, message = "Allowed file types must not exceed 500 characters")
    @Column(name = "allowed_file_types", length = 500)
    private String allowedFileTypes;
    
    @Size(max = 1000, message = "Instructions must not exceed 1000 characters")
    @Column(name = "instructions", length = 1000)
    private String instructions;
    
    @Column(name = "is_group_assignment", nullable = false)
    private Boolean isGroupAssignment = false;
    
    @Column(name = "max_group_size")
    private Integer maxGroupSize;
    
    @Column(name = "auto_grade", nullable = false)
    private Boolean autoGrade = false;
    
    @Column(name = "plagiarism_check", nullable = false)
    private Boolean plagiarismCheck = false;
    
    // Relationships
    @OneToMany(mappedBy = "assignment", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private Set<Submission> submissions = new HashSet<>();
    
    @OneToMany(mappedBy = "assignment", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private Set<Grade> grades = new HashSet<>();
    
    // Constructors
    public Assignment() {}
    
    public Assignment(String title, Course course, User teacher, LocalDateTime dueDate) {
        this.title = title;
        this.course = course;
        this.teacher = teacher;
        this.dueDate = dueDate;
    }
    
    // Getters and Setters
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Course getCourse() {
        return course;
    }
    
    public void setCourse(Course course) {
        this.course = course;
    }
    
    public User getTeacher() {
        return teacher;
    }
    
    public void setTeacher(User teacher) {
        this.teacher = teacher;
    }
    
    public LocalDateTime getDueDate() {
        return dueDate;
    }
    
    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }
    
    public Double getMaxPoints() {
        return maxPoints;
    }
    
    public void setMaxPoints(Double maxPoints) {
        this.maxPoints = maxPoints;
    }
    
    public AssignmentStatus getStatus() {
        return status;
    }
    
    public void setStatus(AssignmentStatus status) {
        this.status = status;
    }
    
    public SubmissionType getSubmissionType() {
        return submissionType;
    }
    
    public void setSubmissionType(SubmissionType submissionType) {
        this.submissionType = submissionType;
    }
    
    public Boolean getAllowLateSubmission() {
        return allowLateSubmission;
    }
    
    public void setAllowLateSubmission(Boolean allowLateSubmission) {
        this.allowLateSubmission = allowLateSubmission;
    }
    
    public Double getLatePenaltyPercentage() {
        return latePenaltyPercentage;
    }
    
    public void setLatePenaltyPercentage(Double latePenaltyPercentage) {
        this.latePenaltyPercentage = latePenaltyPercentage;
    }
    
    public Integer getMaxFileSizeMb() {
        return maxFileSizeMb;
    }
    
    public void setMaxFileSizeMb(Integer maxFileSizeMb) {
        this.maxFileSizeMb = maxFileSizeMb;
    }
    
    public String getAllowedFileTypes() {
        return allowedFileTypes;
    }
    
    public void setAllowedFileTypes(String allowedFileTypes) {
        this.allowedFileTypes = allowedFileTypes;
    }
    
    public String getInstructions() {
        return instructions;
    }
    
    public void setInstructions(String instructions) {
        this.instructions = instructions;
    }
    
    public Boolean getIsGroupAssignment() {
        return isGroupAssignment;
    }
    
    public void setIsGroupAssignment(Boolean isGroupAssignment) {
        this.isGroupAssignment = isGroupAssignment;
    }
    
    public Integer getMaxGroupSize() {
        return maxGroupSize;
    }
    
    public void setMaxGroupSize(Integer maxGroupSize) {
        this.maxGroupSize = maxGroupSize;
    }
    
    public Boolean getAutoGrade() {
        return autoGrade;
    }
    
    public void setAutoGrade(Boolean autoGrade) {
        this.autoGrade = autoGrade;
    }
    
    public Boolean getPlagiarismCheck() {
        return plagiarismCheck;
    }
    
    public void setPlagiarismCheck(Boolean plagiarismCheck) {
        this.plagiarismCheck = plagiarismCheck;
    }
    
    public Set<Submission> getSubmissions() {
        return submissions;
    }
    
    public void setSubmissions(Set<Submission> submissions) {
        this.submissions = submissions;
    }
    
    public Set<Grade> getGrades() {
        return grades;
    }
    
    public void setGrades(Set<Grade> grades) {
        this.grades = grades;
    }
    
    // Utility methods
    public boolean isOverdue() {
        return dueDate != null && LocalDateTime.now().isAfter(dueDate);
    }
    
    public boolean canSubmit() {
        return status == AssignmentStatus.PUBLISHED && 
               (allowLateSubmission || !isOverdue());
    }
    
    public void publish() {
        this.status = AssignmentStatus.PUBLISHED;
    }
    
    public void unpublish() {
        this.status = AssignmentStatus.DRAFT;
    }
    
    public void close() {
        this.status = AssignmentStatus.CLOSED;
    }
    
    @Override
    public String toString() {
        return "Assignment{" +
                "id=" + getId() +
                ", title='" + title + '\'' +
                ", course=" + (course != null ? course.getName() : "null") +
                ", teacher=" + (teacher != null ? teacher.getFullName() : "null") +
                ", status=" + status +
                ", dueDate=" + dueDate +
                ", maxPoints=" + maxPoints +
                '}';
    }
    
    // Enums
    public enum AssignmentStatus {
        DRAFT, PUBLISHED, CLOSED, ARCHIVED
    }
    
    public enum SubmissionType {
        FILE_UPLOAD, TEXT_ENTRY, ONLINE_URL, MEDIA_RECORDING
    }
}
