package com.lmssoft.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Size;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * Attendance entity for tracking student and staff attendance
 * Supports both course-based and general attendance tracking
 */
@Entity
@Table(name = "attendance", indexes = {
    @Index(name = "idx_attendance_user", columnList = "user_id"),
    @Index(name = "idx_attendance_date", columnList = "attendance_date"),
    @Index(name = "idx_attendance_status", columnList = "status")
})
public class Attendance extends BaseEntity {
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "course_id")
    private Course course;
    
    @Column(name = "attendance_date", nullable = false)
    private LocalDate attendanceDate;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private AttendanceStatus status = AttendanceStatus.PRESENT;
    
    @Column(name = "check_in_time")
    private LocalTime checkInTime;
    
    @Column(name = "check_out_time")
    private LocalTime checkOutTime;
    
    @Size(max = 500, message = "Notes must not exceed 500 characters")
    @Column(name = "notes", length = 500)
    private String notes;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "marked_by")
    private User markedBy;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "attendance_type", nullable = false, length = 20)
    private AttendanceType attendanceType = AttendanceType.CLASS;
    
    @Column(name = "is_excused", nullable = false)
    private Boolean isExcused = false;
    
    @Size(max = 500, message = "Excuse reason must not exceed 500 characters")
    @Column(name = "excuse_reason", length = 500)
    private String excuseReason;
    
    @Column(name = "location", length = 100)
    private String location;
    
    @Column(name = "ip_address", length = 45)
    private String ipAddress;
    
    @Column(name = "device_info", length = 200)
    private String deviceInfo;
    
    // Constructors
    public Attendance() {}
    
    public Attendance(User user, LocalDate attendanceDate, AttendanceStatus status) {
        this.user = user;
        this.attendanceDate = attendanceDate;
        this.status = status;
    }
    
    public Attendance(User user, Course course, LocalDate attendanceDate, AttendanceStatus status) {
        this.user = user;
        this.course = course;
        this.attendanceDate = attendanceDate;
        this.status = status;
    }
    
    // Getters and Setters
    public User getUser() {
        return user;
    }
    
    public void setUser(User user) {
        this.user = user;
    }
    
    public Course getCourse() {
        return course;
    }
    
    public void setCourse(Course course) {
        this.course = course;
    }
    
    public LocalDate getAttendanceDate() {
        return attendanceDate;
    }
    
    public void setAttendanceDate(LocalDate attendanceDate) {
        this.attendanceDate = attendanceDate;
    }
    
    public AttendanceStatus getStatus() {
        return status;
    }
    
    public void setStatus(AttendanceStatus status) {
        this.status = status;
    }
    
    public LocalTime getCheckInTime() {
        return checkInTime;
    }
    
    public void setCheckInTime(LocalTime checkInTime) {
        this.checkInTime = checkInTime;
    }
    
    public LocalTime getCheckOutTime() {
        return checkOutTime;
    }
    
    public void setCheckOutTime(LocalTime checkOutTime) {
        this.checkOutTime = checkOutTime;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public User getMarkedBy() {
        return markedBy;
    }
    
    public void setMarkedBy(User markedBy) {
        this.markedBy = markedBy;
    }
    
    public AttendanceType getAttendanceType() {
        return attendanceType;
    }
    
    public void setAttendanceType(AttendanceType attendanceType) {
        this.attendanceType = attendanceType;
    }
    
    public Boolean getIsExcused() {
        return isExcused;
    }
    
    public void setIsExcused(Boolean isExcused) {
        this.isExcused = isExcused;
    }
    
    public String getExcuseReason() {
        return excuseReason;
    }
    
    public void setExcuseReason(String excuseReason) {
        this.excuseReason = excuseReason;
    }
    
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
    
    public String getIpAddress() {
        return ipAddress;
    }
    
    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
    
    public String getDeviceInfo() {
        return deviceInfo;
    }
    
    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }
    
    // Utility methods
    public void checkIn() {
        this.checkInTime = LocalTime.now();
        this.status = AttendanceStatus.PRESENT;
    }
    
    public void checkOut() {
        this.checkOutTime = LocalTime.now();
    }
    
    public void markAsExcused(String reason) {
        this.isExcused = true;
        this.excuseReason = reason;
        if (this.status == AttendanceStatus.ABSENT) {
            this.status = AttendanceStatus.EXCUSED;
        }
    }
    
    public boolean isPresent() {
        return status == AttendanceStatus.PRESENT;
    }
    
    public boolean isAbsent() {
        return status == AttendanceStatus.ABSENT;
    }
    
    public boolean isLate() {
        return status == AttendanceStatus.LATE;
    }
    
    @Override
    public String toString() {
        return "Attendance{" +
                "id=" + getId() +
                ", user=" + (user != null ? user.getFullName() : "null") +
                ", course=" + (course != null ? course.getName() : "null") +
                ", attendanceDate=" + attendanceDate +
                ", status=" + status +
                ", attendanceType=" + attendanceType +
                ", isExcused=" + isExcused +
                '}';
    }
    
    // Enums
    public enum AttendanceStatus {
        PRESENT, ABSENT, LATE, EXCUSED, PARTIAL
    }
    
    public enum AttendanceType {
        CLASS, EXAM, EVENT, MEETING, GENERAL
    }
}
