package com.lmssoft.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;

/**
 * Course entity representing academic courses in the LMS
 * Contains course information, enrollment, and relationships
 */
@Entity
@Table(name = "courses", indexes = {
    @Index(name = "idx_course_code", columnList = "course_code"),
    @Index(name = "idx_course_status", columnList = "status")
})
public class Course extends BaseEntity {
    
    @NotBlank(message = "Course code is required")
    @Size(max = 20, message = "Course code must not exceed 20 characters")
    @Column(name = "course_code", nullable = false, unique = true, length = 20)
    private String courseCode;
    
    @NotBlank(message = "Course name is required")
    @Size(max = 100, message = "Course name must not exceed 100 characters")
    @Column(name = "name", nullable = false, length = 100)
    private String name;
    
    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    @Column(name = "description", length = 1000)
    private String description;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "teacher_id", nullable = false)
    private User teacher;
    
    @Column(name = "credits")
    private Integer credits;
    
    @Column(name = "start_date")
    private LocalDate startDate;
    
    @Column(name = "end_date")
    private LocalDate endDate;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private CourseStatus status = CourseStatus.DRAFT;
    
    @Column(name = "max_enrollment")
    private Integer maxEnrollment;
    
    @Column(name = "current_enrollment", nullable = false)
    private Integer currentEnrollment = 0;
    
    @Size(max = 50, message = "Semester must not exceed 50 characters")
    @Column(name = "semester", length = 50)
    private String semester;
    
    @Size(max = 10, message = "Academic year must not exceed 10 characters")
    @Column(name = "academic_year", length = 10)
    private String academicYear;
    
    @Size(max = 100, message = "Department must not exceed 100 characters")
    @Column(name = "department", length = 100)
    private String department;
    
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
    
    @Column(name = "allow_self_enrollment", nullable = false)
    private Boolean allowSelfEnrollment = false;
    
    @Size(max = 255, message = "Course image URL must not exceed 255 characters")
    @Column(name = "course_image_url", length = 255)
    private String courseImageUrl;
    
    // Relationships
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "course_enrollments",
        joinColumns = @JoinColumn(name = "course_id"),
        inverseJoinColumns = @JoinColumn(name = "student_id")
    )
    private Set<User> enrolledStudents = new HashSet<>();
    
    @OneToMany(mappedBy = "course", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private Set<Test> tests = new HashSet<>();
    
    @OneToMany(mappedBy = "course", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private Set<Assignment> assignments = new HashSet<>();
    
    @OneToMany(mappedBy = "course", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private Set<Resource> resources = new HashSet<>();
    
    @OneToMany(mappedBy = "course", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private Set<Grade> grades = new HashSet<>();
    
    // Constructors
    public Course() {}
    
    public Course(String courseCode, String name, User teacher) {
        this.courseCode = courseCode;
        this.name = name;
        this.teacher = teacher;
    }
    
    // Getters and Setters
    public String getCourseCode() {
        return courseCode;
    }
    
    public void setCourseCode(String courseCode) {
        this.courseCode = courseCode;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public User getTeacher() {
        return teacher;
    }
    
    public void setTeacher(User teacher) {
        this.teacher = teacher;
    }
    
    public Integer getCredits() {
        return credits;
    }
    
    public void setCredits(Integer credits) {
        this.credits = credits;
    }
    
    public LocalDate getStartDate() {
        return startDate;
    }
    
    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }
    
    public LocalDate getEndDate() {
        return endDate;
    }
    
    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }
    
    public CourseStatus getStatus() {
        return status;
    }
    
    public void setStatus(CourseStatus status) {
        this.status = status;
    }
    
    public Integer getMaxEnrollment() {
        return maxEnrollment;
    }
    
    public void setMaxEnrollment(Integer maxEnrollment) {
        this.maxEnrollment = maxEnrollment;
    }
    
    public Integer getCurrentEnrollment() {
        return currentEnrollment;
    }
    
    public void setCurrentEnrollment(Integer currentEnrollment) {
        this.currentEnrollment = currentEnrollment;
    }
    
    public String getSemester() {
        return semester;
    }
    
    public void setSemester(String semester) {
        this.semester = semester;
    }
    
    public String getAcademicYear() {
        return academicYear;
    }
    
    public void setAcademicYear(String academicYear) {
        this.academicYear = academicYear;
    }
    
    public String getDepartment() {
        return department;
    }
    
    public void setDepartment(String department) {
        this.department = department;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public Boolean getAllowSelfEnrollment() {
        return allowSelfEnrollment;
    }
    
    public void setAllowSelfEnrollment(Boolean allowSelfEnrollment) {
        this.allowSelfEnrollment = allowSelfEnrollment;
    }
    
    public String getCourseImageUrl() {
        return courseImageUrl;
    }
    
    public void setCourseImageUrl(String courseImageUrl) {
        this.courseImageUrl = courseImageUrl;
    }
    
    public Set<User> getEnrolledStudents() {
        return enrolledStudents;
    }
    
    public void setEnrolledStudents(Set<User> enrolledStudents) {
        this.enrolledStudents = enrolledStudents;
    }
    
    public Set<Test> getTests() {
        return tests;
    }
    
    public void setTests(Set<Test> tests) {
        this.tests = tests;
    }
    
    public Set<Assignment> getAssignments() {
        return assignments;
    }
    
    public void setAssignments(Set<Assignment> assignments) {
        this.assignments = assignments;
    }
    
    public Set<Resource> getResources() {
        return resources;
    }
    
    public void setResources(Set<Resource> resources) {
        this.resources = resources;
    }
    
    public Set<Grade> getGrades() {
        return grades;
    }
    
    public void setGrades(Set<Grade> grades) {
        this.grades = grades;
    }
    
    // Utility methods
    public boolean canEnroll() {
        return isActive && status == CourseStatus.ACTIVE && 
               (maxEnrollment == null || currentEnrollment < maxEnrollment);
    }
    
    public void enrollStudent(User student) {
        if (canEnroll() && !enrolledStudents.contains(student)) {
            enrolledStudents.add(student);
            currentEnrollment++;
        }
    }
    
    public void unenrollStudent(User student) {
        if (enrolledStudents.remove(student)) {
            currentEnrollment--;
        }
    }
    
    public boolean isStudentEnrolled(User student) {
        return enrolledStudents.contains(student);
    }
    
    public void activate() {
        this.isActive = true;
        this.status = CourseStatus.ACTIVE;
    }
    
    public void deactivate() {
        this.isActive = false;
        this.status = CourseStatus.INACTIVE;
    }
    
    public void complete() {
        this.status = CourseStatus.COMPLETED;
    }
    
    @Override
    public String toString() {
        return "Course{" +
                "id=" + getId() +
                ", courseCode='" + courseCode + '\'' +
                ", name='" + name + '\'' +
                ", teacher=" + (teacher != null ? teacher.getFullName() : "null") +
                ", status=" + status +
                ", currentEnrollment=" + currentEnrollment +
                ", maxEnrollment=" + maxEnrollment +
                '}';
    }
    
    // Course status enum
    public enum CourseStatus {
        DRAFT, ACTIVE, INACTIVE, COMPLETED, CANCELLED
    }
}
