package com.lmssoft.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * Grade entity for tracking student grades
 * Supports grades for assignments, tests, and overall course grades
 */
@Entity
@Table(name = "grades", indexes = {
    @Index(name = "idx_grade_student", columnList = "student_id"),
    @Index(name = "idx_grade_course", columnList = "course_id"),
    @Index(name = "idx_grade_type", columnList = "grade_type")
})
public class Grade extends BaseEntity {
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "student_id", nullable = false)
    private User student;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "course_id", nullable = false)
    private Course course;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assignment_id")
    private Assignment assignment;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "test_id")
    private Test test;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "grade_type", nullable = false, length = 20)
    private GradeType gradeType = GradeType.ASSIGNMENT;
    
    @Column(name = "points_earned", nullable = false)
    private Double pointsEarned = 0.0;
    
    @Column(name = "points_possible", nullable = false)
    private Double pointsPossible = 100.0;
    
    @Column(name = "percentage", nullable = false)
    private Double percentage = 0.0;
    
    @Size(max = 10, message = "Letter grade must not exceed 10 characters")
    @Column(name = "letter_grade", length = 10)
    private String letterGrade;
    
    @Column(name = "gpa_points")
    private Double gpaPoints;
    
    @Size(max = 1000, message = "Comments must not exceed 1000 characters")
    @Column(name = "comments", length = 1000)
    private String comments;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "graded_by", nullable = false)
    private User gradedBy;
    
    @Column(name = "graded_at", nullable = false)
    private LocalDateTime gradedAt;
    
    @Column(name = "is_final", nullable = false)
    private Boolean isFinal = false;
    
    @Column(name = "is_published", nullable = false)
    private Boolean isPublished = false;
    
    @Column(name = "weight")
    private Double weight = 1.0;
    
    @Column(name = "extra_credit", nullable = false)
    private Boolean extraCredit = false;
    
    @Column(name = "late_penalty")
    private Double latePenalty = 0.0;
    
    // Constructors
    public Grade() {}
    
    public Grade(User student, Course course, GradeType gradeType, Double pointsEarned, Double pointsPossible, User gradedBy) {
        this.student = student;
        this.course = course;
        this.gradeType = gradeType;
        this.pointsEarned = pointsEarned;
        this.pointsPossible = pointsPossible;
        this.gradedBy = gradedBy;
        this.gradedAt = LocalDateTime.now();
        calculatePercentage();
        calculateLetterGrade();
    }
    
    // Getters and Setters
    public User getStudent() {
        return student;
    }
    
    public void setStudent(User student) {
        this.student = student;
    }
    
    public Course getCourse() {
        return course;
    }
    
    public void setCourse(Course course) {
        this.course = course;
    }
    
    public Assignment getAssignment() {
        return assignment;
    }
    
    public void setAssignment(Assignment assignment) {
        this.assignment = assignment;
    }
    
    public Test getTest() {
        return test;
    }
    
    public void setTest(Test test) {
        this.test = test;
    }
    
    public GradeType getGradeType() {
        return gradeType;
    }
    
    public void setGradeType(GradeType gradeType) {
        this.gradeType = gradeType;
    }
    
    public Double getPointsEarned() {
        return pointsEarned;
    }
    
    public void setPointsEarned(Double pointsEarned) {
        this.pointsEarned = pointsEarned;
        calculatePercentage();
        calculateLetterGrade();
    }
    
    public Double getPointsPossible() {
        return pointsPossible;
    }
    
    public void setPointsPossible(Double pointsPossible) {
        this.pointsPossible = pointsPossible;
        calculatePercentage();
        calculateLetterGrade();
    }
    
    public Double getPercentage() {
        return percentage;
    }
    
    public void setPercentage(Double percentage) {
        this.percentage = percentage;
    }
    
    public String getLetterGrade() {
        return letterGrade;
    }
    
    public void setLetterGrade(String letterGrade) {
        this.letterGrade = letterGrade;
    }
    
    public Double getGpaPoints() {
        return gpaPoints;
    }
    
    public void setGpaPoints(Double gpaPoints) {
        this.gpaPoints = gpaPoints;
    }
    
    public String getComments() {
        return comments;
    }
    
    public void setComments(String comments) {
        this.comments = comments;
    }
    
    public User getGradedBy() {
        return gradedBy;
    }
    
    public void setGradedBy(User gradedBy) {
        this.gradedBy = gradedBy;
    }
    
    public LocalDateTime getGradedAt() {
        return gradedAt;
    }
    
    public void setGradedAt(LocalDateTime gradedAt) {
        this.gradedAt = gradedAt;
    }
    
    public Boolean getIsFinal() {
        return isFinal;
    }
    
    public void setIsFinal(Boolean isFinal) {
        this.isFinal = isFinal;
    }
    
    public Boolean getIsPublished() {
        return isPublished;
    }
    
    public void setIsPublished(Boolean isPublished) {
        this.isPublished = isPublished;
    }
    
    public Double getWeight() {
        return weight;
    }
    
    public void setWeight(Double weight) {
        this.weight = weight;
    }
    
    public Boolean getExtraCredit() {
        return extraCredit;
    }
    
    public void setExtraCredit(Boolean extraCredit) {
        this.extraCredit = extraCredit;
    }
    
    public Double getLatePenalty() {
        return latePenalty;
    }
    
    public void setLatePenalty(Double latePenalty) {
        this.latePenalty = latePenalty;
    }
    
    // Utility methods
    public void calculatePercentage() {
        if (pointsPossible != null && pointsPossible > 0) {
            this.percentage = (pointsEarned / pointsPossible) * 100;
        } else {
            this.percentage = 0.0;
        }
    }
    
    public void calculateLetterGrade() {
        if (percentage >= 90) {
            this.letterGrade = "A";
            this.gpaPoints = 4.0;
        } else if (percentage >= 80) {
            this.letterGrade = "B";
            this.gpaPoints = 3.0;
        } else if (percentage >= 70) {
            this.letterGrade = "C";
            this.gpaPoints = 2.0;
        } else if (percentage >= 60) {
            this.letterGrade = "D";
            this.gpaPoints = 1.0;
        } else {
            this.letterGrade = "F";
            this.gpaPoints = 0.0;
        }
    }
    
    public void publish() {
        this.isPublished = true;
    }
    
    public void unpublish() {
        this.isPublished = false;
    }
    
    public void applyLatePenalty(double penaltyPercentage) {
        this.latePenalty = penaltyPercentage;
        double penaltyAmount = pointsEarned * (penaltyPercentage / 100);
        this.pointsEarned = Math.max(0, pointsEarned - penaltyAmount);
        calculatePercentage();
        calculateLetterGrade();
    }
    
    @Override
    public String toString() {
        return "Grade{" +
                "id=" + getId() +
                ", student=" + (student != null ? student.getFullName() : "null") +
                ", course=" + (course != null ? course.getName() : "null") +
                ", gradeType=" + gradeType +
                ", pointsEarned=" + pointsEarned +
                ", pointsPossible=" + pointsPossible +
                ", percentage=" + percentage +
                ", letterGrade='" + letterGrade + '\'' +
                ", isPublished=" + isPublished +
                '}';
    }
    
    // Enum
    public enum GradeType {
        ASSIGNMENT, TEST, QUIZ, EXAM, PROJECT, PARTICIPATION, FINAL, MIDTERM
    }
}
