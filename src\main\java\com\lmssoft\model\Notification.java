package com.lmssoft.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * Notification entity for system notifications
 * Supports various types of notifications for users
 */
@Entity
@Table(name = "notifications", indexes = {
    @Index(name = "idx_notification_recipient", columnList = "recipient_id"),
    @Index(name = "idx_notification_type", columnList = "notification_type"),
    @Index(name = "idx_notification_read", columnList = "is_read"),
    @Index(name = "idx_notification_created", columnList = "created_at")
})
public class Notification extends BaseEntity {
    
    @NotBlank(message = "Notification title is required")
    @Size(max = 200, message = "Notification title must not exceed 200 characters")
    @Column(name = "title", nullable = false, length = 200)
    private String title;
    
    @NotBlank(message = "Notification message is required")
    @Size(max = 1000, message = "Notification message must not exceed 1000 characters")
    @Column(name = "message", nullable = false, length = 1000)
    private String message;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "recipient_id", nullable = false)
    private User recipient;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sender_id")
    private User sender;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "notification_type", nullable = false, length = 30)
    private NotificationType notificationType = NotificationType.GENERAL;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "priority", nullable = false, length = 20)
    private Priority priority = Priority.NORMAL;
    
    @Column(name = "is_read", nullable = false)
    private Boolean isRead = false;
    
    @Column(name = "read_at")
    private LocalDateTime readAt;
    
    @Column(name = "is_sent", nullable = false)
    private Boolean isSent = false;
    
    @Column(name = "sent_at")
    private LocalDateTime sentAt;
    
    @Size(max = 500, message = "Action URL must not exceed 500 characters")
    @Column(name = "action_url", length = 500)
    private String actionUrl;
    
    @Size(max = 100, message = "Action text must not exceed 100 characters")
    @Column(name = "action_text", length = 100)
    private String actionText;
    
    @Column(name = "expires_at")
    private LocalDateTime expiresAt;
    
    @Column(name = "related_entity_id")
    private Long relatedEntityId;
    
    @Size(max = 50, message = "Related entity type must not exceed 50 characters")
    @Column(name = "related_entity_type", length = 50)
    private String relatedEntityType;
    
    @Size(max = 500, message = "Metadata must not exceed 500 characters")
    @Column(name = "metadata", length = 500)
    private String metadata;
    
    // Constructors
    public Notification() {}
    
    public Notification(String title, String message, User recipient, NotificationType notificationType) {
        this.title = title;
        this.message = message;
        this.recipient = recipient;
        this.notificationType = notificationType;
    }
    
    public Notification(String title, String message, User recipient, User sender, NotificationType notificationType) {
        this.title = title;
        this.message = message;
        this.recipient = recipient;
        this.sender = sender;
        this.notificationType = notificationType;
    }
    
    // Getters and Setters
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public User getRecipient() {
        return recipient;
    }
    
    public void setRecipient(User recipient) {
        this.recipient = recipient;
    }
    
    public User getSender() {
        return sender;
    }
    
    public void setSender(User sender) {
        this.sender = sender;
    }
    
    public NotificationType getNotificationType() {
        return notificationType;
    }
    
    public void setNotificationType(NotificationType notificationType) {
        this.notificationType = notificationType;
    }
    
    public Priority getPriority() {
        return priority;
    }
    
    public void setPriority(Priority priority) {
        this.priority = priority;
    }
    
    public Boolean getIsRead() {
        return isRead;
    }
    
    public void setIsRead(Boolean isRead) {
        this.isRead = isRead;
    }
    
    public LocalDateTime getReadAt() {
        return readAt;
    }
    
    public void setReadAt(LocalDateTime readAt) {
        this.readAt = readAt;
    }
    
    public Boolean getIsSent() {
        return isSent;
    }
    
    public void setIsSent(Boolean isSent) {
        this.isSent = isSent;
    }
    
    public LocalDateTime getSentAt() {
        return sentAt;
    }
    
    public void setSentAt(LocalDateTime sentAt) {
        this.sentAt = sentAt;
    }
    
    public String getActionUrl() {
        return actionUrl;
    }
    
    public void setActionUrl(String actionUrl) {
        this.actionUrl = actionUrl;
    }
    
    public String getActionText() {
        return actionText;
    }
    
    public void setActionText(String actionText) {
        this.actionText = actionText;
    }
    
    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }
    
    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }
    
    public Long getRelatedEntityId() {
        return relatedEntityId;
    }
    
    public void setRelatedEntityId(Long relatedEntityId) {
        this.relatedEntityId = relatedEntityId;
    }
    
    public String getRelatedEntityType() {
        return relatedEntityType;
    }
    
    public void setRelatedEntityType(String relatedEntityType) {
        this.relatedEntityType = relatedEntityType;
    }
    
    public String getMetadata() {
        return metadata;
    }
    
    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }
    
    // Utility methods
    public void markAsRead() {
        this.isRead = true;
        this.readAt = LocalDateTime.now();
    }
    
    public void markAsUnread() {
        this.isRead = false;
        this.readAt = null;
    }
    
    public void markAsSent() {
        this.isSent = true;
        this.sentAt = LocalDateTime.now();
    }
    
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }
    
    public boolean hasAction() {
        return actionUrl != null && !actionUrl.trim().isEmpty();
    }
    
    @Override
    public String toString() {
        return "Notification{" +
                "id=" + getId() +
                ", title='" + title + '\'' +
                ", recipient=" + (recipient != null ? recipient.getFullName() : "null") +
                ", sender=" + (sender != null ? sender.getFullName() : "null") +
                ", notificationType=" + notificationType +
                ", priority=" + priority +
                ", isRead=" + isRead +
                ", isSent=" + isSent +
                '}';
    }
    
    // Enums
    public enum NotificationType {
        GENERAL,
        ASSIGNMENT_DUE,
        ASSIGNMENT_GRADED,
        TEST_AVAILABLE,
        TEST_REMINDER,
        TEST_GRADED,
        COURSE_ANNOUNCEMENT,
        ATTENDANCE_REMINDER,
        SYSTEM_MAINTENANCE,
        ACCOUNT_UPDATE,
        PASSWORD_RESET,
        ENROLLMENT_CONFIRMATION,
        GRADE_PUBLISHED
    }
    
    public enum Priority {
        LOW, NORMAL, HIGH, URGENT
    }
}
