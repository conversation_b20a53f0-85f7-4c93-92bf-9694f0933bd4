package com.lmssoft.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.HashSet;
import java.util.Set;

/**
 * Question entity for CBT system
 * Supports multiple question types with flexible answer options
 */
@Entity
@Table(name = "questions", indexes = {
    @Index(name = "idx_question_type", columnList = "question_type"),
    @Index(name = "idx_question_order", columnList = "question_order")
})
public class Question extends BaseEntity {
    
    @NotBlank(message = "Question text is required")
    @Size(max = 2000, message = "Question text must not exceed 2000 characters")
    @Column(name = "question_text", nullable = false, length = 2000)
    private String questionText;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "question_type", nullable = false, length = 30)
    private QuestionType questionType = QuestionType.MULTIPLE_CHOICE;
    
    @NotNull(message = "Marks is required")
    @Column(name = "marks", nullable = false)
    private Double marks = 1.0;
    
    @Column(name = "question_order")
    private Integer questionOrder;
    
    @Size(max = 1000, message = "Explanation must not exceed 1000 characters")
    @Column(name = "explanation", length = 1000)
    private String explanation;
    
    @Size(max = 255, message = "Image URL must not exceed 255 characters")
    @Column(name = "image_url", length = 255)
    private String imageUrl;
    
    @Column(name = "is_required", nullable = false)
    private Boolean isRequired = true;
    
    @Column(name = "time_limit_seconds")
    private Integer timeLimitSeconds;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "difficulty_level", length = 20)
    private DifficultyLevel difficultyLevel = DifficultyLevel.MEDIUM;
    
    @Size(max = 500, message = "Tags must not exceed 500 characters")
    @Column(name = "tags", length = 500)
    private String tags;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "test_id", nullable = false)
    private Test test;
    
    // Relationships
    @OneToMany(mappedBy = "question", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @OrderBy("answerOrder ASC")
    private Set<Answer> answers = new HashSet<>();
    
    // Constructors
    public Question() {}
    
    public Question(String questionText, QuestionType questionType, Double marks, Test test) {
        this.questionText = questionText;
        this.questionType = questionType;
        this.marks = marks;
        this.test = test;
    }
    
    // Getters and Setters
    public String getQuestionText() {
        return questionText;
    }
    
    public void setQuestionText(String questionText) {
        this.questionText = questionText;
    }
    
    public QuestionType getQuestionType() {
        return questionType;
    }
    
    public void setQuestionType(QuestionType questionType) {
        this.questionType = questionType;
    }
    
    public Double getMarks() {
        return marks;
    }
    
    public void setMarks(Double marks) {
        this.marks = marks;
    }
    
    public Integer getQuestionOrder() {
        return questionOrder;
    }
    
    public void setQuestionOrder(Integer questionOrder) {
        this.questionOrder = questionOrder;
    }
    
    public String getExplanation() {
        return explanation;
    }
    
    public void setExplanation(String explanation) {
        this.explanation = explanation;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public Boolean getIsRequired() {
        return isRequired;
    }
    
    public void setIsRequired(Boolean isRequired) {
        this.isRequired = isRequired;
    }
    
    public Integer getTimeLimitSeconds() {
        return timeLimitSeconds;
    }
    
    public void setTimeLimitSeconds(Integer timeLimitSeconds) {
        this.timeLimitSeconds = timeLimitSeconds;
    }
    
    public DifficultyLevel getDifficultyLevel() {
        return difficultyLevel;
    }
    
    public void setDifficultyLevel(DifficultyLevel difficultyLevel) {
        this.difficultyLevel = difficultyLevel;
    }
    
    public String getTags() {
        return tags;
    }
    
    public void setTags(String tags) {
        this.tags = tags;
    }
    
    public Test getTest() {
        return test;
    }
    
    public void setTest(Test test) {
        this.test = test;
    }
    
    public Set<Answer> getAnswers() {
        return answers;
    }
    
    public void setAnswers(Set<Answer> answers) {
        this.answers = answers;
    }
    
    // Utility methods
    public void addAnswer(Answer answer) {
        answers.add(answer);
        answer.setQuestion(this);
    }
    
    public void removeAnswer(Answer answer) {
        answers.remove(answer);
        answer.setQuestion(null);
    }
    
    public Answer getCorrectAnswer() {
        return answers.stream()
            .filter(Answer::getIsCorrect)
            .findFirst()
            .orElse(null);
    }
    
    public Set<Answer> getCorrectAnswers() {
        return answers.stream()
            .filter(Answer::getIsCorrect)
            .collect(java.util.stream.Collectors.toSet());
    }
    
    @Override
    public String toString() {
        return "Question{" +
                "id=" + getId() +
                ", questionText='" + (questionText.length() > 50 ? 
                    questionText.substring(0, 50) + "..." : questionText) + '\'' +
                ", questionType=" + questionType +
                ", marks=" + marks +
                ", answersCount=" + answers.size() +
                '}';
    }
    
    // Enums
    public enum QuestionType {
        MULTIPLE_CHOICE,
        MULTIPLE_SELECT,
        TRUE_FALSE,
        SHORT_ANSWER,
        ESSAY,
        FILL_IN_BLANK,
        MATCHING,
        ORDERING
    }
    
    public enum DifficultyLevel {
        EASY, MEDIUM, HARD
    }
}
