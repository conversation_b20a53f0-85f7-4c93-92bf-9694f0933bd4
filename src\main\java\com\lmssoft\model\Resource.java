package com.lmssoft.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * Resource entity for course materials and files
 * Supports various types of educational resources
 */
@Entity
@Table(name = "resources", indexes = {
    @Index(name = "idx_resource_course", columnList = "course_id"),
    @Index(name = "idx_resource_type", columnList = "resource_type"),
    @Index(name = "idx_resource_visible", columnList = "is_visible")
})
public class Resource extends BaseEntity {
    
    @NotBlank(message = "Resource title is required")
    @Size(max = 200, message = "Resource title must not exceed 200 characters")
    @Column(name = "title", nullable = false, length = 200)
    private String title;
    
    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    @Column(name = "description", length = 1000)
    private String description;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "course_id", nullable = false)
    private Course course;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "resource_type", nullable = false, length = 20)
    private ResourceType resourceType = ResourceType.FILE;
    
    @Size(max = 255, message = "File path must not exceed 255 characters")
    @Column(name = "file_path", length = 255)
    private String filePath;
    
    @Size(max = 100, message = "File name must not exceed 100 characters")
    @Column(name = "file_name", length = 100)
    private String fileName;
    
    @Column(name = "file_size_bytes")
    private Long fileSizeBytes;
    
    @Size(max = 50, message = "File type must not exceed 50 characters")
    @Column(name = "file_type", length = 50)
    private String fileType;
    
    @Size(max = 500, message = "URL must not exceed 500 characters")
    @Column(name = "url", length = 500)
    private String url;
    
    @Size(max = 5000, message = "Content must not exceed 5000 characters")
    @Column(name = "content", length = 5000)
    private String content;
    
    @Column(name = "is_visible", nullable = false)
    private Boolean isVisible = true;
    
    @Column(name = "is_downloadable", nullable = false)
    private Boolean isDownloadable = true;
    
    @Column(name = "download_count", nullable = false)
    private Integer downloadCount = 0;
    
    @Column(name = "view_count", nullable = false)
    private Integer viewCount = 0;
    
    @Column(name = "resource_order")
    private Integer resourceOrder;
    
    @Size(max = 500, message = "Tags must not exceed 500 characters")
    @Column(name = "tags", length = 500)
    private String tags;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "uploaded_by", nullable = false)
    private User uploadedBy;
    
    // Constructors
    public Resource() {}
    
    public Resource(String title, Course course, ResourceType resourceType, User uploadedBy) {
        this.title = title;
        this.course = course;
        this.resourceType = resourceType;
        this.uploadedBy = uploadedBy;
    }
    
    // Getters and Setters
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Course getCourse() {
        return course;
    }
    
    public void setCourse(Course course) {
        this.course = course;
    }
    
    public ResourceType getResourceType() {
        return resourceType;
    }
    
    public void setResourceType(ResourceType resourceType) {
        this.resourceType = resourceType;
    }
    
    public String getFilePath() {
        return filePath;
    }
    
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    
    public Long getFileSizeBytes() {
        return fileSizeBytes;
    }
    
    public void setFileSizeBytes(Long fileSizeBytes) {
        this.fileSizeBytes = fileSizeBytes;
    }
    
    public String getFileType() {
        return fileType;
    }
    
    public void setFileType(String fileType) {
        this.fileType = fileType;
    }
    
    public String getUrl() {
        return url;
    }
    
    public void setUrl(String url) {
        this.url = url;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public Boolean getIsVisible() {
        return isVisible;
    }
    
    public void setIsVisible(Boolean isVisible) {
        this.isVisible = isVisible;
    }
    
    public Boolean getIsDownloadable() {
        return isDownloadable;
    }
    
    public void setIsDownloadable(Boolean isDownloadable) {
        this.isDownloadable = isDownloadable;
    }
    
    public Integer getDownloadCount() {
        return downloadCount;
    }
    
    public void setDownloadCount(Integer downloadCount) {
        this.downloadCount = downloadCount;
    }
    
    public Integer getViewCount() {
        return viewCount;
    }
    
    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }
    
    public Integer getResourceOrder() {
        return resourceOrder;
    }
    
    public void setResourceOrder(Integer resourceOrder) {
        this.resourceOrder = resourceOrder;
    }
    
    public String getTags() {
        return tags;
    }
    
    public void setTags(String tags) {
        this.tags = tags;
    }
    
    public User getUploadedBy() {
        return uploadedBy;
    }
    
    public void setUploadedBy(User uploadedBy) {
        this.uploadedBy = uploadedBy;
    }
    
    // Utility methods
    public void incrementDownloadCount() {
        this.downloadCount++;
    }
    
    public void incrementViewCount() {
        this.viewCount++;
    }
    
    public String getFileSizeFormatted() {
        if (fileSizeBytes == null) return "0 B";
        
        long bytes = fileSizeBytes;
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1f MB", bytes / (1024.0 * 1024));
        return String.format("%.1f GB", bytes / (1024.0 * 1024 * 1024));
    }
    
    public void hide() {
        this.isVisible = false;
    }
    
    public void show() {
        this.isVisible = true;
    }
    
    @Override
    public String toString() {
        return "Resource{" +
                "id=" + getId() +
                ", title='" + title + '\'' +
                ", course=" + (course != null ? course.getName() : "null") +
                ", resourceType=" + resourceType +
                ", fileName='" + fileName + '\'' +
                ", isVisible=" + isVisible +
                ", downloadCount=" + downloadCount +
                ", viewCount=" + viewCount +
                '}';
    }
    
    // Enum
    public enum ResourceType {
        FILE, URL, TEXT, VIDEO, AUDIO, IMAGE, DOCUMENT, PRESENTATION, SPREADSHEET
    }
}
