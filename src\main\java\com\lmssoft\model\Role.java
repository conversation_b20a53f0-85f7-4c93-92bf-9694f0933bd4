package com.lmssoft.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.util.HashSet;
import java.util.Set;

/**
 * Role entity for role-based access control (RBAC)
 * Defines user roles and their permissions
 */
@Entity
@Table(name = "roles")
public class Role extends BaseEntity {
    
    @NotBlank(message = "Role name is required")
    @Size(max = 50, message = "Role name must not exceed 50 characters")
    @Column(name = "name", nullable = false, unique = true, length = 50)
    private String name;
    
    @Size(max = 255, message = "Description must not exceed 255 characters")
    @Column(name = "description", length = 255)
    private String description;
    
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
    
    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(name = "role_permissions", joinColumns = @JoinColumn(name = "role_id"))
    @Column(name = "permission")
    private Set<String> permissions = new HashSet<>();
    
    @OneToMany(mappedBy = "role", fetch = FetchType.LAZY)
    private Set<User> users = new HashSet<>();
    
    // Constructors
    public Role() {}
    
    public Role(String name, String description) {
        this.name = name;
        this.description = description;
    }
    
    // Getters and Setters
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public Set<String> getPermissions() {
        return permissions;
    }
    
    public void setPermissions(Set<String> permissions) {
        this.permissions = permissions;
    }
    
    public Set<User> getUsers() {
        return users;
    }
    
    public void setUsers(Set<User> users) {
        this.users = users;
    }
    
    // Utility methods
    public void addPermission(String permission) {
        this.permissions.add(permission);
    }
    
    public void removePermission(String permission) {
        this.permissions.remove(permission);
    }
    
    public boolean hasPermission(String permission) {
        return this.permissions.contains(permission);
    }
    
    public void activate() {
        this.isActive = true;
    }
    
    public void deactivate() {
        this.isActive = false;
    }
    
    @Override
    public String toString() {
        return "Role{" +
                "id=" + getId() +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", isActive=" + isActive +
                ", permissionsCount=" + permissions.size() +
                '}';
    }
    
    // Common role constants
    public static final String STUDENT = "STUDENT";
    public static final String TEACHER = "TEACHER";
    public static final String ADMINISTRATOR = "ADMINISTRATOR";
    public static final String STAFF = "STAFF";
    
    // Permission constants
    public static final String PERM_USER_READ = "USER_READ";
    public static final String PERM_USER_WRITE = "USER_WRITE";
    public static final String PERM_USER_DELETE = "USER_DELETE";
    
    public static final String PERM_COURSE_READ = "COURSE_READ";
    public static final String PERM_COURSE_WRITE = "COURSE_WRITE";
    public static final String PERM_COURSE_DELETE = "COURSE_DELETE";
    
    public static final String PERM_TEST_READ = "TEST_READ";
    public static final String PERM_TEST_WRITE = "TEST_WRITE";
    public static final String PERM_TEST_DELETE = "TEST_DELETE";
    public static final String PERM_TEST_TAKE = "TEST_TAKE";
    public static final String PERM_TEST_GRADE = "TEST_GRADE";
    
    public static final String PERM_ATTENDANCE_READ = "ATTENDANCE_READ";
    public static final String PERM_ATTENDANCE_WRITE = "ATTENDANCE_WRITE";
    
    public static final String PERM_REPORT_READ = "REPORT_READ";
    public static final String PERM_REPORT_GENERATE = "REPORT_GENERATE";
    
    public static final String PERM_SYSTEM_ADMIN = "SYSTEM_ADMIN";
}
