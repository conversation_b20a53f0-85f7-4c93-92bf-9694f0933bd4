package com.lmssoft.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * Submission entity for assignment submissions
 * Tracks student submissions for assignments
 */
@Entity
@Table(name = "submissions", indexes = {
    @Index(name = "idx_submission_student", columnList = "student_id"),
    @Index(name = "idx_submission_assignment", columnList = "assignment_id"),
    @Index(name = "idx_submission_status", columnList = "status")
})
public class Submission extends BaseEntity {
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assignment_id", nullable = false)
    private Assignment assignment;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "student_id", nullable = false)
    private User student;
    
    @Column(name = "submitted_at", nullable = false)
    private LocalDateTime submittedAt;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private SubmissionStatus status = SubmissionStatus.SUBMITTED;
    
    @Size(max = 5000, message = "Text content must not exceed 5000 characters")
    @Column(name = "text_content", length = 5000)
    private String textContent;
    
    @Size(max = 255, message = "File path must not exceed 255 characters")
    @Column(name = "file_path", length = 255)
    private String filePath;
    
    @Size(max = 100, message = "File name must not exceed 100 characters")
    @Column(name = "file_name", length = 100)
    private String fileName;
    
    @Column(name = "file_size_bytes")
    private Long fileSizeBytes;
    
    @Size(max = 50, message = "File type must not exceed 50 characters")
    @Column(name = "file_type", length = 50)
    private String fileType;
    
    @Size(max = 500, message = "URL must not exceed 500 characters")
    @Column(name = "submission_url", length = 500)
    private String submissionUrl;
    
    @Column(name = "is_late", nullable = false)
    private Boolean isLate = false;
    
    @Column(name = "attempt_number", nullable = false)
    private Integer attemptNumber = 1;
    
    @Size(max = 1000, message = "Comments must not exceed 1000 characters")
    @Column(name = "student_comments", length = 1000)
    private String studentComments;
    
    @Size(max = 1000, message = "Teacher feedback must not exceed 1000 characters")
    @Column(name = "teacher_feedback", length = 1000)
    private String teacherFeedback;
    
    @Column(name = "graded_at")
    private LocalDateTime gradedAt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "graded_by")
    private User gradedBy;
    
    @Column(name = "plagiarism_score")
    private Double plagiarismScore;
    
    @Size(max = 1000, message = "Plagiarism details must not exceed 1000 characters")
    @Column(name = "plagiarism_details", length = 1000)
    private String plagiarismDetails;
    
    // Constructors
    public Submission() {}
    
    public Submission(Assignment assignment, User student) {
        this.assignment = assignment;
        this.student = student;
        this.submittedAt = LocalDateTime.now();
        this.isLate = assignment.getDueDate() != null && 
                     LocalDateTime.now().isAfter(assignment.getDueDate());
    }
    
    // Getters and Setters
    public Assignment getAssignment() {
        return assignment;
    }
    
    public void setAssignment(Assignment assignment) {
        this.assignment = assignment;
    }
    
    public User getStudent() {
        return student;
    }
    
    public void setStudent(User student) {
        this.student = student;
    }
    
    public LocalDateTime getSubmittedAt() {
        return submittedAt;
    }
    
    public void setSubmittedAt(LocalDateTime submittedAt) {
        this.submittedAt = submittedAt;
    }
    
    public SubmissionStatus getStatus() {
        return status;
    }
    
    public void setStatus(SubmissionStatus status) {
        this.status = status;
    }
    
    public String getTextContent() {
        return textContent;
    }
    
    public void setTextContent(String textContent) {
        this.textContent = textContent;
    }
    
    public String getFilePath() {
        return filePath;
    }
    
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    
    public Long getFileSizeBytes() {
        return fileSizeBytes;
    }
    
    public void setFileSizeBytes(Long fileSizeBytes) {
        this.fileSizeBytes = fileSizeBytes;
    }
    
    public String getFileType() {
        return fileType;
    }
    
    public void setFileType(String fileType) {
        this.fileType = fileType;
    }
    
    public String getSubmissionUrl() {
        return submissionUrl;
    }
    
    public void setSubmissionUrl(String submissionUrl) {
        this.submissionUrl = submissionUrl;
    }
    
    public Boolean getIsLate() {
        return isLate;
    }
    
    public void setIsLate(Boolean isLate) {
        this.isLate = isLate;
    }
    
    public Integer getAttemptNumber() {
        return attemptNumber;
    }
    
    public void setAttemptNumber(Integer attemptNumber) {
        this.attemptNumber = attemptNumber;
    }
    
    public String getStudentComments() {
        return studentComments;
    }
    
    public void setStudentComments(String studentComments) {
        this.studentComments = studentComments;
    }
    
    public String getTeacherFeedback() {
        return teacherFeedback;
    }
    
    public void setTeacherFeedback(String teacherFeedback) {
        this.teacherFeedback = teacherFeedback;
    }
    
    public LocalDateTime getGradedAt() {
        return gradedAt;
    }
    
    public void setGradedAt(LocalDateTime gradedAt) {
        this.gradedAt = gradedAt;
    }
    
    public User getGradedBy() {
        return gradedBy;
    }
    
    public void setGradedBy(User gradedBy) {
        this.gradedBy = gradedBy;
    }
    
    public Double getPlagiarismScore() {
        return plagiarismScore;
    }
    
    public void setPlagiarismScore(Double plagiarismScore) {
        this.plagiarismScore = plagiarismScore;
    }
    
    public String getPlagiarismDetails() {
        return plagiarismDetails;
    }
    
    public void setPlagiarismDetails(String plagiarismDetails) {
        this.plagiarismDetails = plagiarismDetails;
    }
    
    // Utility methods
    public boolean isGraded() {
        return status == SubmissionStatus.GRADED;
    }
    
    public void markAsGraded(User gradedBy) {
        this.status = SubmissionStatus.GRADED;
        this.gradedAt = LocalDateTime.now();
        this.gradedBy = gradedBy;
    }
    
    public String getFileSizeFormatted() {
        if (fileSizeBytes == null) return "0 B";
        
        long bytes = fileSizeBytes;
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1f MB", bytes / (1024.0 * 1024));
        return String.format("%.1f GB", bytes / (1024.0 * 1024 * 1024));
    }
    
    @Override
    public String toString() {
        return "Submission{" +
                "id=" + getId() +
                ", assignment=" + (assignment != null ? assignment.getTitle() : "null") +
                ", student=" + (student != null ? student.getFullName() : "null") +
                ", status=" + status +
                ", submittedAt=" + submittedAt +
                ", isLate=" + isLate +
                ", attemptNumber=" + attemptNumber +
                '}';
    }
    
    // Enum
    public enum SubmissionStatus {
        SUBMITTED, GRADED, RETURNED, RESUBMITTED
    }
}
