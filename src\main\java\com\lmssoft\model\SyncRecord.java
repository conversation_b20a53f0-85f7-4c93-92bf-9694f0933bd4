package com.lmssoft.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * SyncRecord entity for tracking offline-online synchronization
 * Manages data synchronization between local and remote databases
 */
@Entity
@Table(name = "sync_records", indexes = {
    @Index(name = "idx_sync_entity_type", columnList = "entity_type"),
    @Index(name = "idx_sync_status", columnList = "sync_status"),
    @Index(name = "idx_sync_last_attempt", columnList = "last_sync_attempt"),
    @Index(name = "idx_sync_entity_id", columnList = "entity_id")
})
public class SyncRecord extends BaseEntity {
    
    @NotBlank(message = "Entity type is required")
    @Size(max = 50, message = "Entity type must not exceed 50 characters")
    @Column(name = "entity_type", nullable = false, length = 50)
    private String entityType;
    
    @Column(name = "entity_id", nullable = false)
    private Long entityId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "sync_status", nullable = false, length = 20)
    private SyncStatus syncStatus = SyncStatus.PENDING;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "operation_type", nullable = false, length = 20)
    private OperationType operationType = OperationType.CREATE;
    
    @Column(name = "last_sync_attempt")
    private LocalDateTime lastSyncAttempt;
    
    @Column(name = "last_successful_sync")
    private LocalDateTime lastSuccessfulSync;
    
    @Column(name = "sync_attempts", nullable = false)
    private Integer syncAttempts = 0;
    
    @Column(name = "max_retry_attempts", nullable = false)
    private Integer maxRetryAttempts = 3;
    
    @Size(max = 1000, message = "Error message must not exceed 1000 characters")
    @Column(name = "error_message", length = 1000)
    private String errorMessage;
    
    @Size(max = 2000, message = "Error details must not exceed 2000 characters")
    @Column(name = "error_details", length = 2000)
    private String errorDetails;
    
    @Column(name = "data_hash", length = 64)
    private String dataHash;
    
    @Column(name = "remote_id")
    private Long remoteId;
    
    @Column(name = "conflict_detected", nullable = false)
    private Boolean conflictDetected = false;
    
    @Size(max = 1000, message = "Conflict details must not exceed 1000 characters")
    @Column(name = "conflict_details", length = 1000)
    private String conflictDetails;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "conflict_resolution", length = 20)
    private ConflictResolution conflictResolution;
    
    @Column(name = "priority", nullable = false)
    private Integer priority = 5;
    
    @Size(max = 500, message = "Metadata must not exceed 500 characters")
    @Column(name = "metadata", length = 500)
    private String metadata;
    
    // Constructors
    public SyncRecord() {}
    
    public SyncRecord(String entityType, Long entityId, OperationType operationType) {
        this.entityType = entityType;
        this.entityId = entityId;
        this.operationType = operationType;
    }
    
    // Getters and Setters
    public String getEntityType() {
        return entityType;
    }
    
    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }
    
    public Long getEntityId() {
        return entityId;
    }
    
    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }
    
    public SyncStatus getSyncStatus() {
        return syncStatus;
    }
    
    public void setSyncStatus(SyncStatus syncStatus) {
        this.syncStatus = syncStatus;
    }
    
    public OperationType getOperationType() {
        return operationType;
    }
    
    public void setOperationType(OperationType operationType) {
        this.operationType = operationType;
    }
    
    public LocalDateTime getLastSyncAttempt() {
        return lastSyncAttempt;
    }
    
    public void setLastSyncAttempt(LocalDateTime lastSyncAttempt) {
        this.lastSyncAttempt = lastSyncAttempt;
    }
    
    public LocalDateTime getLastSuccessfulSync() {
        return lastSuccessfulSync;
    }
    
    public void setLastSuccessfulSync(LocalDateTime lastSuccessfulSync) {
        this.lastSuccessfulSync = lastSuccessfulSync;
    }
    
    public Integer getSyncAttempts() {
        return syncAttempts;
    }
    
    public void setSyncAttempts(Integer syncAttempts) {
        this.syncAttempts = syncAttempts;
    }
    
    public Integer getMaxRetryAttempts() {
        return maxRetryAttempts;
    }
    
    public void setMaxRetryAttempts(Integer maxRetryAttempts) {
        this.maxRetryAttempts = maxRetryAttempts;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public String getErrorDetails() {
        return errorDetails;
    }
    
    public void setErrorDetails(String errorDetails) {
        this.errorDetails = errorDetails;
    }
    
    public String getDataHash() {
        return dataHash;
    }
    
    public void setDataHash(String dataHash) {
        this.dataHash = dataHash;
    }
    
    public Long getRemoteId() {
        return remoteId;
    }
    
    public void setRemoteId(Long remoteId) {
        this.remoteId = remoteId;
    }
    
    public Boolean getConflictDetected() {
        return conflictDetected;
    }
    
    public void setConflictDetected(Boolean conflictDetected) {
        this.conflictDetected = conflictDetected;
    }
    
    public String getConflictDetails() {
        return conflictDetails;
    }
    
    public void setConflictDetails(String conflictDetails) {
        this.conflictDetails = conflictDetails;
    }
    
    public ConflictResolution getConflictResolution() {
        return conflictResolution;
    }
    
    public void setConflictResolution(ConflictResolution conflictResolution) {
        this.conflictResolution = conflictResolution;
    }
    
    public Integer getPriority() {
        return priority;
    }
    
    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    
    public String getMetadata() {
        return metadata;
    }
    
    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }
    
    // Utility methods
    public void markSyncAttempt() {
        this.syncAttempts++;
        this.lastSyncAttempt = LocalDateTime.now();
    }
    
    public void markSyncSuccess() {
        this.syncStatus = SyncStatus.SYNCED;
        this.lastSuccessfulSync = LocalDateTime.now();
        this.errorMessage = null;
        this.errorDetails = null;
    }
    
    public void markSyncFailed(String errorMessage, String errorDetails) {
        this.syncStatus = SyncStatus.FAILED;
        this.errorMessage = errorMessage;
        this.errorDetails = errorDetails;
        
        if (syncAttempts >= maxRetryAttempts) {
            this.syncStatus = SyncStatus.FAILED_PERMANENTLY;
        }
    }
    
    public void markConflict(String conflictDetails) {
        this.conflictDetected = true;
        this.conflictDetails = conflictDetails;
        this.syncStatus = SyncStatus.CONFLICT;
    }
    
    public void resolveConflict(ConflictResolution resolution) {
        this.conflictResolution = resolution;
        this.conflictDetected = false;
        this.syncStatus = SyncStatus.PENDING;
    }
    
    public boolean canRetry() {
        return syncAttempts < maxRetryAttempts && 
               syncStatus != SyncStatus.FAILED_PERMANENTLY &&
               syncStatus != SyncStatus.SYNCED;
    }
    
    public boolean needsSync() {
        return syncStatus == SyncStatus.PENDING || 
               syncStatus == SyncStatus.FAILED;
    }
    
    @Override
    public String toString() {
        return "SyncRecord{" +
                "id=" + getId() +
                ", entityType='" + entityType + '\'' +
                ", entityId=" + entityId +
                ", syncStatus=" + syncStatus +
                ", operationType=" + operationType +
                ", syncAttempts=" + syncAttempts +
                ", conflictDetected=" + conflictDetected +
                '}';
    }
    
    // Enums
    public enum SyncStatus {
        PENDING, IN_PROGRESS, SYNCED, FAILED, FAILED_PERMANENTLY, CONFLICT
    }
    
    public enum OperationType {
        CREATE, UPDATE, DELETE
    }
    
    public enum ConflictResolution {
        USE_LOCAL, USE_REMOTE, MERGE, MANUAL
    }
}
