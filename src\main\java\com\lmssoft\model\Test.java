package com.lmssoft.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * Test entity for Computer-Based Testing (CBT) system
 * Represents tests/exams that students can take
 */
@Entity
@Table(name = "tests", indexes = {
    @Index(name = "idx_test_status", columnList = "status"),
    @Index(name = "idx_test_start_time", columnList = "start_time"),
    @Index(name = "idx_test_end_time", columnList = "end_time")
})
public class Test extends BaseEntity {
    
    @NotBlank(message = "Test title is required")
    @Size(max = 200, message = "Test title must not exceed 200 characters")
    @Column(name = "title", nullable = false, length = 200)
    private String title;
    
    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    @Column(name = "description", length = 1000)
    private String description;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "course_id")
    private Course course;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by_user_id", nullable = false)
    private User createdByUser;
    
    @NotNull(message = "Duration is required")
    @Column(name = "duration_minutes", nullable = false)
    private Integer durationMinutes;
    
    @Column(name = "start_time")
    private LocalDateTime startTime;
    
    @Column(name = "end_time")
    private LocalDateTime endTime;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private TestStatus status = TestStatus.DRAFT;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "test_type", nullable = false, length = 20)
    private TestType testType = TestType.QUIZ;
    
    @Column(name = "max_attempts", nullable = false)
    private Integer maxAttempts = 1;
    
    @Column(name = "passing_score")
    private Double passingScore;
    
    @Column(name = "total_marks", nullable = false)
    private Double totalMarks = 0.0;
    
    @Column(name = "shuffle_questions", nullable = false)
    private Boolean shuffleQuestions = false;
    
    @Column(name = "shuffle_answers", nullable = false)
    private Boolean shuffleAnswers = false;
    
    @Column(name = "show_results_immediately", nullable = false)
    private Boolean showResultsImmediately = true;
    
    @Column(name = "allow_review", nullable = false)
    private Boolean allowReview = true;
    
    @Column(name = "require_password", nullable = false)
    private Boolean requirePassword = false;
    
    @Size(max = 100, message = "Test password must not exceed 100 characters")
    @Column(name = "test_password", length = 100)
    private String testPassword;
    
    @Column(name = "is_proctored", nullable = false)
    private Boolean isProctored = false;
    
    @Column(name = "auto_submit", nullable = false)
    private Boolean autoSubmit = true;
    
    @Column(name = "prevent_copy_paste", nullable = false)
    private Boolean preventCopyPaste = true;
    
    @Column(name = "full_screen_mode", nullable = false)
    private Boolean fullScreenMode = false;
    
    @Size(max = 1000, message = "Instructions must not exceed 1000 characters")
    @Column(name = "instructions", length = 1000)
    private String instructions;
    
    // Relationships
    @OneToMany(mappedBy = "test", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private Set<Question> questions = new HashSet<>();
    
    @OneToMany(mappedBy = "test", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private Set<TestResult> testResults = new HashSet<>();
    
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "test_allowed_students",
        joinColumns = @JoinColumn(name = "test_id"),
        inverseJoinColumns = @JoinColumn(name = "student_id")
    )
    private Set<User> allowedStudents = new HashSet<>();
    
    // Constructors
    public Test() {}
    
    public Test(String title, Course course, User createdByUser, Integer durationMinutes) {
        this.title = title;
        this.course = course;
        this.createdByUser = createdByUser;
        this.durationMinutes = durationMinutes;
    }
    
    // Getters and Setters
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Course getCourse() {
        return course;
    }
    
    public void setCourse(Course course) {
        this.course = course;
    }
    
    public User getCreatedByUser() {
        return createdByUser;
    }
    
    public void setCreatedByUser(User createdByUser) {
        this.createdByUser = createdByUser;
    }
    
    public Integer getDurationMinutes() {
        return durationMinutes;
    }
    
    public void setDurationMinutes(Integer durationMinutes) {
        this.durationMinutes = durationMinutes;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    
    public TestStatus getStatus() {
        return status;
    }
    
    public void setStatus(TestStatus status) {
        this.status = status;
    }
    
    public TestType getTestType() {
        return testType;
    }
    
    public void setTestType(TestType testType) {
        this.testType = testType;
    }
    
    public Integer getMaxAttempts() {
        return maxAttempts;
    }
    
    public void setMaxAttempts(Integer maxAttempts) {
        this.maxAttempts = maxAttempts;
    }
    
    public Double getPassingScore() {
        return passingScore;
    }
    
    public void setPassingScore(Double passingScore) {
        this.passingScore = passingScore;
    }
    
    public Double getTotalMarks() {
        return totalMarks;
    }
    
    public void setTotalMarks(Double totalMarks) {
        this.totalMarks = totalMarks;
    }
    
    public Boolean getShuffleQuestions() {
        return shuffleQuestions;
    }
    
    public void setShuffleQuestions(Boolean shuffleQuestions) {
        this.shuffleQuestions = shuffleQuestions;
    }
    
    public Boolean getShuffleAnswers() {
        return shuffleAnswers;
    }
    
    public void setShuffleAnswers(Boolean shuffleAnswers) {
        this.shuffleAnswers = shuffleAnswers;
    }
    
    public Boolean getShowResultsImmediately() {
        return showResultsImmediately;
    }
    
    public void setShowResultsImmediately(Boolean showResultsImmediately) {
        this.showResultsImmediately = showResultsImmediately;
    }
    
    public Boolean getAllowReview() {
        return allowReview;
    }
    
    public void setAllowReview(Boolean allowReview) {
        this.allowReview = allowReview;
    }
    
    public Boolean getRequirePassword() {
        return requirePassword;
    }
    
    public void setRequirePassword(Boolean requirePassword) {
        this.requirePassword = requirePassword;
    }
    
    public String getTestPassword() {
        return testPassword;
    }
    
    public void setTestPassword(String testPassword) {
        this.testPassword = testPassword;
    }
    
    public Boolean getIsProctored() {
        return isProctored;
    }
    
    public void setIsProctored(Boolean isProctored) {
        this.isProctored = isProctored;
    }
    
    public Boolean getAutoSubmit() {
        return autoSubmit;
    }
    
    public void setAutoSubmit(Boolean autoSubmit) {
        this.autoSubmit = autoSubmit;
    }
    
    public Boolean getPreventCopyPaste() {
        return preventCopyPaste;
    }
    
    public void setPreventCopyPaste(Boolean preventCopyPaste) {
        this.preventCopyPaste = preventCopyPaste;
    }
    
    public Boolean getFullScreenMode() {
        return fullScreenMode;
    }
    
    public void setFullScreenMode(Boolean fullScreenMode) {
        this.fullScreenMode = fullScreenMode;
    }
    
    public String getInstructions() {
        return instructions;
    }
    
    public void setInstructions(String instructions) {
        this.instructions = instructions;
    }
    
    public Set<Question> getQuestions() {
        return questions;
    }
    
    public void setQuestions(Set<Question> questions) {
        this.questions = questions;
    }
    
    public Set<TestResult> getTestResults() {
        return testResults;
    }
    
    public void setTestResults(Set<TestResult> testResults) {
        this.testResults = testResults;
    }
    
    public Set<User> getAllowedStudents() {
        return allowedStudents;
    }
    
    public void setAllowedStudents(Set<User> allowedStudents) {
        this.allowedStudents = allowedStudents;
    }
    
    // Utility methods
    public boolean isActive() {
        LocalDateTime now = LocalDateTime.now();
        return status == TestStatus.PUBLISHED && 
               (startTime == null || startTime.isBefore(now) || startTime.isEqual(now)) &&
               (endTime == null || endTime.isAfter(now));
    }
    
    public boolean isAvailableForStudent(User student) {
        return isActive() && (allowedStudents.isEmpty() || allowedStudents.contains(student));
    }
    
    public void addQuestion(Question question) {
        questions.add(question);
        question.setTest(this);
        recalculateTotalMarks();
    }
    
    public void removeQuestion(Question question) {
        questions.remove(question);
        question.setTest(null);
        recalculateTotalMarks();
    }
    
    public void recalculateTotalMarks() {
        totalMarks = questions.stream()
            .mapToDouble(Question::getMarks)
            .sum();
    }
    
    public void publish() {
        this.status = TestStatus.PUBLISHED;
    }
    
    public void unpublish() {
        this.status = TestStatus.DRAFT;
    }
    
    public void archive() {
        this.status = TestStatus.ARCHIVED;
    }
    
    @Override
    public String toString() {
        return "Test{" +
                "id=" + getId() +
                ", title='" + title + '\'' +
                ", course=" + (course != null ? course.getName() : "null") +
                ", status=" + status +
                ", durationMinutes=" + durationMinutes +
                ", totalMarks=" + totalMarks +
                ", questionsCount=" + questions.size() +
                '}';
    }
    
    // Enums
    public enum TestStatus {
        DRAFT, PUBLISHED, ARCHIVED, CANCELLED
    }
    
    public enum TestType {
        QUIZ, EXAM, ASSIGNMENT, PRACTICE
    }
}
