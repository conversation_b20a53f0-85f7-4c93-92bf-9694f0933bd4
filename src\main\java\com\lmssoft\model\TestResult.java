package com.lmssoft.model;

import jakarta.persistence.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * TestResult entity for storing student test attempts and results
 * Tracks test performance and analytics
 */
@Entity
@Table(name = "test_results", indexes = {
    @Index(name = "idx_test_result_student", columnList = "student_id"),
    @Index(name = "idx_test_result_test", columnList = "test_id"),
    @Index(name = "idx_test_result_status", columnList = "status"),
    @Index(name = "idx_test_result_submitted", columnList = "submitted_at")
})
public class TestResult extends BaseEntity {
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "test_id", nullable = false)
    private Test test;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "student_id", nullable = false)
    private User student;
    
    @Column(name = "attempt_number", nullable = false)
    private Integer attemptNumber = 1;
    
    @Column(name = "started_at", nullable = false)
    private LocalDateTime startedAt;
    
    @Column(name = "submitted_at")
    private LocalDateTime submittedAt;
    
    @Column(name = "time_taken_minutes")
    private Integer timeTakenMinutes;
    
    @Column(name = "score", nullable = false)
    private Double score = 0.0;
    
    @Column(name = "percentage", nullable = false)
    private Double percentage = 0.0;
    
    @Column(name = "total_questions", nullable = false)
    private Integer totalQuestions = 0;
    
    @Column(name = "correct_answers", nullable = false)
    private Integer correctAnswers = 0;
    
    @Column(name = "incorrect_answers", nullable = false)
    private Integer incorrectAnswers = 0;
    
    @Column(name = "unanswered_questions", nullable = false)
    private Integer unansweredQuestions = 0;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private TestResultStatus status = TestResultStatus.IN_PROGRESS;
    
    @Column(name = "is_passed")
    private Boolean isPassed;
    
    @Column(name = "grade", length = 10)
    private String grade;
    
    @Column(name = "auto_submitted", nullable = false)
    private Boolean autoSubmitted = false;
    
    @Column(name = "ip_address", length = 45)
    private String ipAddress;
    
    @Column(name = "user_agent", length = 500)
    private String userAgent;
    
    @Column(name = "browser_info", length = 1000)
    private String browserInfo;
    
    @Column(name = "proctoring_violations", nullable = false)
    private Integer proctoringViolations = 0;
    
    @Column(name = "violation_details", length = 2000)
    private String violationDetails;
    
    // Store student answers as JSON
    @ElementCollection(fetch = FetchType.LAZY)
    @CollectionTable(name = "test_result_answers", joinColumns = @JoinColumn(name = "test_result_id"))
    @MapKeyColumn(name = "question_id")
    @Column(name = "answer_data", length = 2000)
    private Map<Long, String> studentAnswers = new HashMap<>();
    
    // Constructors
    public TestResult() {}
    
    public TestResult(Test test, User student, Integer attemptNumber) {
        this.test = test;
        this.student = student;
        this.attemptNumber = attemptNumber;
        this.startedAt = LocalDateTime.now();
        this.totalQuestions = test.getQuestions().size();
    }
    
    // Getters and Setters
    public Test getTest() {
        return test;
    }
    
    public void setTest(Test test) {
        this.test = test;
    }
    
    public User getStudent() {
        return student;
    }
    
    public void setStudent(User student) {
        this.student = student;
    }
    
    public Integer getAttemptNumber() {
        return attemptNumber;
    }
    
    public void setAttemptNumber(Integer attemptNumber) {
        this.attemptNumber = attemptNumber;
    }
    
    public LocalDateTime getStartedAt() {
        return startedAt;
    }
    
    public void setStartedAt(LocalDateTime startedAt) {
        this.startedAt = startedAt;
    }
    
    public LocalDateTime getSubmittedAt() {
        return submittedAt;
    }
    
    public void setSubmittedAt(LocalDateTime submittedAt) {
        this.submittedAt = submittedAt;
    }
    
    public Integer getTimeTakenMinutes() {
        return timeTakenMinutes;
    }
    
    public void setTimeTakenMinutes(Integer timeTakenMinutes) {
        this.timeTakenMinutes = timeTakenMinutes;
    }
    
    public Double getScore() {
        return score;
    }
    
    public void setScore(Double score) {
        this.score = score;
    }
    
    public Double getPercentage() {
        return percentage;
    }
    
    public void setPercentage(Double percentage) {
        this.percentage = percentage;
    }
    
    public Integer getTotalQuestions() {
        return totalQuestions;
    }
    
    public void setTotalQuestions(Integer totalQuestions) {
        this.totalQuestions = totalQuestions;
    }
    
    public Integer getCorrectAnswers() {
        return correctAnswers;
    }
    
    public void setCorrectAnswers(Integer correctAnswers) {
        this.correctAnswers = correctAnswers;
    }
    
    public Integer getIncorrectAnswers() {
        return incorrectAnswers;
    }
    
    public void setIncorrectAnswers(Integer incorrectAnswers) {
        this.incorrectAnswers = incorrectAnswers;
    }
    
    public Integer getUnansweredQuestions() {
        return unansweredQuestions;
    }
    
    public void setUnansweredQuestions(Integer unansweredQuestions) {
        this.unansweredQuestions = unansweredQuestions;
    }
    
    public TestResultStatus getStatus() {
        return status;
    }
    
    public void setStatus(TestResultStatus status) {
        this.status = status;
    }
    
    public Boolean getIsPassed() {
        return isPassed;
    }
    
    public void setIsPassed(Boolean isPassed) {
        this.isPassed = isPassed;
    }
    
    public String getGrade() {
        return grade;
    }
    
    public void setGrade(String grade) {
        this.grade = grade;
    }
    
    public Boolean getAutoSubmitted() {
        return autoSubmitted;
    }
    
    public void setAutoSubmitted(Boolean autoSubmitted) {
        this.autoSubmitted = autoSubmitted;
    }
    
    public String getIpAddress() {
        return ipAddress;
    }
    
    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
    
    public String getUserAgent() {
        return userAgent;
    }
    
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }
    
    public String getBrowserInfo() {
        return browserInfo;
    }
    
    public void setBrowserInfo(String browserInfo) {
        this.browserInfo = browserInfo;
    }
    
    public Integer getProctoringViolations() {
        return proctoringViolations;
    }
    
    public void setProctoringViolations(Integer proctoringViolations) {
        this.proctoringViolations = proctoringViolations;
    }
    
    public String getViolationDetails() {
        return violationDetails;
    }
    
    public void setViolationDetails(String violationDetails) {
        this.violationDetails = violationDetails;
    }
    
    public Map<Long, String> getStudentAnswers() {
        return studentAnswers;
    }
    
    public void setStudentAnswers(Map<Long, String> studentAnswers) {
        this.studentAnswers = studentAnswers;
    }
    
    // Utility methods
    public void submitTest() {
        this.submittedAt = LocalDateTime.now();
        this.status = TestResultStatus.SUBMITTED;
        calculateTimeTaken();
    }
    
    public void calculateTimeTaken() {
        if (startedAt != null && submittedAt != null) {
            long minutes = java.time.Duration.between(startedAt, submittedAt).toMinutes();
            this.timeTakenMinutes = (int) minutes;
        }
    }
    
    public void calculateResults() {
        if (test != null && test.getTotalMarks() > 0) {
            this.percentage = (score / test.getTotalMarks()) * 100;
            
            // Determine if passed
            if (test.getPassingScore() != null) {
                this.isPassed = percentage >= test.getPassingScore();
            }
            
            // Calculate grade
            this.grade = calculateGrade(percentage);
        }
    }
    
    private String calculateGrade(double percentage) {
        if (percentage >= 90) return "A";
        if (percentage >= 80) return "B";
        if (percentage >= 70) return "C";
        if (percentage >= 60) return "D";
        return "F";
    }
    
    public void addViolation(String violation) {
        this.proctoringViolations++;
        if (this.violationDetails == null) {
            this.violationDetails = violation;
        } else {
            this.violationDetails += "; " + violation;
        }
    }
    
    @Override
    public String toString() {
        return "TestResult{" +
                "id=" + getId() +
                ", test=" + (test != null ? test.getTitle() : "null") +
                ", student=" + (student != null ? student.getFullName() : "null") +
                ", attemptNumber=" + attemptNumber +
                ", status=" + status +
                ", score=" + score +
                ", percentage=" + percentage +
                '}';
    }
    
    // Enum
    public enum TestResultStatus {
        IN_PROGRESS, SUBMITTED, GRADED, CANCELLED
    }
}
