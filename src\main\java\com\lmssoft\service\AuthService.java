package com.lmssoft.service;

import at.favre.lib.crypto.bcrypt.BCrypt;
import com.lmssoft.config.AppConfig;
import com.lmssoft.model.Role;
import com.lmssoft.model.User;
import com.lmssoft.util.JwtUtil;
import org.hibernate.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * Authentication service for user login, registration, and session management
 * Implements secure authentication with password hashing and JWT tokens
 */
public class AuthService {
    
    private static final Logger logger = LoggerFactory.getLogger(AuthService.class);
    private static AuthService instance;
    
    private final DatabaseService dbService;
    private final AppConfig config;
    private User currentUser;
    private String currentToken;
    
    // Password validation patterns
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[A-Za-z0-9+_.-]+@([A-Za-z0-9.-]+\\.[A-Za-z]{2,})$"
    );
    
    private AuthService() {
        this.dbService = DatabaseService.getInstance();
        this.config = AppConfig.getInstance();
    }
    
    public static AuthService getInstance() {
        if (instance == null) {
            synchronized (AuthService.class) {
                if (instance == null) {
                    instance = new AuthService();
                }
            }
        }
        return instance;
    }
    
    /**
     * Authenticate user with username/email and password
     */
    public AuthResult authenticate(String usernameOrEmail, String password) {
        try {
            logger.debug("Attempting authentication for user: {}", usernameOrEmail);
            
            // Input validation
            if (usernameOrEmail == null || usernameOrEmail.trim().isEmpty()) {
                return AuthResult.failure("Username or email is required");
            }
            
            if (password == null || password.trim().isEmpty()) {
                return AuthResult.failure("Password is required");
            }
            
            // Find user by username or email
            Optional<User> userOpt = findUserByUsernameOrEmail(usernameOrEmail.trim());
            
            if (userOpt.isEmpty()) {
                logger.warn("Authentication failed - user not found: {}", usernameOrEmail);
                return AuthResult.failure("Invalid username/email or password");
            }
            
            User user = userOpt.get();
            
            // Check if account is active
            if (!user.getIsActive()) {
                logger.warn("Authentication failed - account inactive: {}", usernameOrEmail);
                return AuthResult.failure("Account is inactive");
            }
            
            // Check if account is locked
            if (user.isAccountLocked()) {
                logger.warn("Authentication failed - account locked: {}", usernameOrEmail);
                return AuthResult.failure("Account is temporarily locked due to multiple failed login attempts");
            }
            
            // Verify password
            if (!verifyPassword(password, user.getPasswordHash())) {
                handleFailedLogin(user);
                logger.warn("Authentication failed - invalid password for user: {}", usernameOrEmail);
                return AuthResult.failure("Invalid username/email or password");
            }
            
            // Check if user is verified
            if (!user.getIsVerified()) {
                logger.warn("Authentication failed - account not verified: {}", usernameOrEmail);
                return AuthResult.failure("Account is not verified. Please check your email for verification instructions.");
            }
            
            // Authentication successful
            handleSuccessfulLogin(user);
            
            // Generate JWT token
            String token = JwtUtil.generateToken(user);
            
            // Set current user and token
            this.currentUser = user;
            this.currentToken = token;
            
            logger.info("Authentication successful for user: {} ({})", user.getUsername(), user.getRole().getName());
            
            return AuthResult.success(user, token);
            
        } catch (Exception e) {
            logger.error("Authentication error for user: {}", usernameOrEmail, e);
            return AuthResult.failure("Authentication failed due to system error");
        }
    }
    
    /**
     * Register new user
     */
    public AuthResult register(String username, String email, String password, String firstName, String lastName, String roleName) {
        try {
            logger.debug("Attempting registration for user: {}", username);
            
            // Validate input
            ValidationResult validation = validateRegistrationInput(username, email, password, firstName, lastName);
            if (!validation.isValid()) {
                return AuthResult.failure(validation.getErrorMessage());
            }
            
            // Check if username already exists
            if (isUsernameExists(username)) {
                return AuthResult.failure("Username already exists");
            }
            
            // Check if email already exists
            if (isEmailExists(email)) {
                return AuthResult.failure("Email already exists");
            }
            
            // Find role
            Optional<Role> roleOpt = findRoleByName(roleName != null ? roleName : Role.STUDENT);
            if (roleOpt.isEmpty()) {
                return AuthResult.failure("Invalid role specified");
            }
            
            // Hash password
            String passwordHash = hashPassword(password);
            
            // Create user
            User user = new User(username, email, passwordHash, firstName, lastName, roleOpt.get());
            user.setIsVerified(true); // Auto-verify for now, can be changed later
            
            // Save user
            dbService.executeWithTransaction(session -> {
                session.persist(user);
            });
            
            logger.info("User registered successfully: {} ({})", username, roleName);
            
            return AuthResult.success(user, null);
            
        } catch (Exception e) {
            logger.error("Registration error for user: {}", username, e);
            return AuthResult.failure("Registration failed due to system error");
        }
    }
    
    /**
     * Logout current user
     */
    public void logout() {
        if (currentUser != null) {
            logger.info("User logged out: {}", currentUser.getUsername());
            currentUser = null;
            currentToken = null;
        }
    }
    
    /**
     * Get current authenticated user
     */
    public User getCurrentUser() {
        return currentUser;
    }
    
    /**
     * Get current JWT token
     */
    public String getCurrentToken() {
        return currentToken;
    }
    
    /**
     * Check if user is authenticated
     */
    public boolean isAuthenticated() {
        return currentUser != null && currentToken != null;
    }
    
    /**
     * Check if current user has specific role
     */
    public boolean hasRole(String roleName) {
        return currentUser != null && currentUser.hasRole(roleName);
    }
    
    /**
     * Check if current user has specific permission
     */
    public boolean hasPermission(String permission) {
        return currentUser != null && currentUser.hasPermission(permission);
    }
    
    /**
     * Change user password
     */
    public AuthResult changePassword(String currentPassword, String newPassword) {
        if (currentUser == null) {
            return AuthResult.failure("User not authenticated");
        }
        
        try {
            // Verify current password
            if (!verifyPassword(currentPassword, currentUser.getPasswordHash())) {
                return AuthResult.failure("Current password is incorrect");
            }
            
            // Validate new password
            ValidationResult validation = validatePassword(newPassword);
            if (!validation.isValid()) {
                return AuthResult.failure(validation.getErrorMessage());
            }
            
            // Hash new password
            String newPasswordHash = hashPassword(newPassword);
            
            // Update password
            dbService.executeWithTransaction(session -> {
                User user = session.get(User.class, currentUser.getId());
                user.setPasswordHash(newPasswordHash);
                session.merge(user);
            });
            
            // Update current user
            currentUser.setPasswordHash(newPasswordHash);
            
            logger.info("Password changed successfully for user: {}", currentUser.getUsername());
            
            return AuthResult.success(currentUser, currentToken);
            
        } catch (Exception e) {
            logger.error("Password change error for user: {}", currentUser.getUsername(), e);
            return AuthResult.failure("Password change failed due to system error");
        }
    }
    
    /**
     * Validate JWT token and set current user
     */
    public boolean validateToken(String token) {
        try {
            if (token == null || token.trim().isEmpty()) {
                return false;
            }
            
            // Validate token
            if (!JwtUtil.validateToken(token)) {
                return false;
            }
            
            // Extract user ID from token
            Long userId = JwtUtil.getUserIdFromToken(token);
            if (userId == null) {
                return false;
            }
            
            // Load user from database
            Optional<User> userOpt = findUserById(userId);
            if (userOpt.isEmpty()) {
                return false;
            }
            
            User user = userOpt.get();
            
            // Check if user is still active
            if (!user.getIsActive() || user.isAccountLocked()) {
                return false;
            }
            
            // Set current user and token
            this.currentUser = user;
            this.currentToken = token;
            
            return true;
            
        } catch (Exception e) {
            logger.error("Token validation error", e);
            return false;
        }
    }
    
    // Private helper methods
    
    private Optional<User> findUserByUsernameOrEmail(String usernameOrEmail) {
        return dbService.executeReadOnly(session -> {
            String hql = "FROM User u WHERE (u.username = :usernameOrEmail OR u.email = :usernameOrEmail) AND u.isDeleted = false";
            return session.createQuery(hql, User.class)
                .setParameter("usernameOrEmail", usernameOrEmail)
                .uniqueResultOptional();
        });
    }
    
    private Optional<User> findUserById(Long userId) {
        return dbService.executeReadOnly(session -> {
            User user = session.get(User.class, userId);
            return user != null && !user.getIsDeleted() ? Optional.of(user) : Optional.empty();
        });
    }
    
    private Optional<Role> findRoleByName(String roleName) {
        return dbService.executeReadOnly(session -> {
            String hql = "FROM Role r WHERE r.name = :roleName AND r.isActive = true AND r.isDeleted = false";
            return session.createQuery(hql, Role.class)
                .setParameter("roleName", roleName)
                .uniqueResultOptional();
        });
    }
    
    private boolean isUsernameExists(String username) {
        return dbService.executeReadOnly(session -> {
            String hql = "SELECT COUNT(*) FROM User u WHERE u.username = :username AND u.isDeleted = false";
            Long count = session.createQuery(hql, Long.class)
                .setParameter("username", username)
                .getSingleResult();
            return count > 0;
        });
    }
    
    private boolean isEmailExists(String email) {
        return dbService.executeReadOnly(session -> {
            String hql = "SELECT COUNT(*) FROM User u WHERE u.email = :email AND u.isDeleted = false";
            Long count = session.createQuery(hql, Long.class)
                .setParameter("email", email)
                .getSingleResult();
            return count > 0;
        });
    }
    
    private void handleSuccessfulLogin(User user) {
        dbService.executeWithTransaction(session -> {
            User managedUser = session.get(User.class, user.getId());
            managedUser.setLastLogin(LocalDateTime.now());
            managedUser.resetFailedLoginAttempts();
            managedUser.unlockAccount();
            session.merge(managedUser);
        });
    }
    
    private void handleFailedLogin(User user) {
        dbService.executeWithTransaction(session -> {
            User managedUser = session.get(User.class, user.getId());
            managedUser.incrementFailedLoginAttempts();
            
            // Lock account after 5 failed attempts for 30 minutes
            if (managedUser.getFailedLoginAttempts() >= 5) {
                managedUser.lockAccount(30);
            }
            
            session.merge(managedUser);
        });
    }
    
    private String hashPassword(String password) {
        return BCrypt.withDefaults().hashToString(12, password.toCharArray());
    }
    
    private boolean verifyPassword(String password, String hash) {
        return BCrypt.verifyer().verify(password.toCharArray(), hash).verified;
    }
    
    private ValidationResult validateRegistrationInput(String username, String email, String password, String firstName, String lastName) {
        if (username == null || username.trim().length() < 3) {
            return ValidationResult.invalid("Username must be at least 3 characters long");
        }
        
        if (email == null || !EMAIL_PATTERN.matcher(email).matches()) {
            return ValidationResult.invalid("Invalid email format");
        }
        
        ValidationResult passwordValidation = validatePassword(password);
        if (!passwordValidation.isValid()) {
            return passwordValidation;
        }
        
        if (firstName == null || firstName.trim().isEmpty()) {
            return ValidationResult.invalid("First name is required");
        }
        
        if (lastName == null || lastName.trim().isEmpty()) {
            return ValidationResult.invalid("Last name is required");
        }
        
        return ValidationResult.valid();
    }
    
    private ValidationResult validatePassword(String password) {
        if (password == null || password.length() < config.getPasswordMinLength()) {
            return ValidationResult.invalid("Password must be at least " + config.getPasswordMinLength() + " characters long");
        }
        
        if (config.isPasswordRequireNumbers() && !password.matches(".*\\d.*")) {
            return ValidationResult.invalid("Password must contain at least one number");
        }
        
        if (config.isPasswordRequireSpecialChars() && !password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) {
            return ValidationResult.invalid("Password must contain at least one special character");
        }
        
        return ValidationResult.valid();
    }
    
    // Helper classes
    
    public static class AuthResult {
        private final boolean success;
        private final String message;
        private final User user;
        private final String token;
        
        private AuthResult(boolean success, String message, User user, String token) {
            this.success = success;
            this.message = message;
            this.user = user;
            this.token = token;
        }
        
        public static AuthResult success(User user, String token) {
            return new AuthResult(true, "Authentication successful", user, token);
        }
        
        public static AuthResult failure(String message) {
            return new AuthResult(false, message, null, null);
        }
        
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public User getUser() { return user; }
        public String getToken() { return token; }
    }
    
    private static class ValidationResult {
        private final boolean valid;
        private final String errorMessage;
        
        private ValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }
        
        public static ValidationResult valid() {
            return new ValidationResult(true, null);
        }
        
        public static ValidationResult invalid(String errorMessage) {
            return new ValidationResult(false, errorMessage);
        }
        
        public boolean isValid() { return valid; }
        public String getErrorMessage() { return errorMessage; }
    }
}
