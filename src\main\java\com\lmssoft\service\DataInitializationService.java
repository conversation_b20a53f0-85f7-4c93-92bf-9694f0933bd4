package com.lmssoft.service;

import com.lmssoft.model.Role;
import com.lmssoft.model.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;

/**
 * Service for initializing default data in the system
 * Creates default roles, permissions, and admin user
 */
public class DataInitializationService {
    
    private static final Logger logger = LoggerFactory.getLogger(DataInitializationService.class);
    
    private final DatabaseService dbService;
    private final AuthService authService;
    
    public DataInitializationService() {
        this.dbService = DatabaseService.getInstance();
        this.authService = AuthService.getInstance();
    }
    
    /**
     * Initialize default system data
     */
    public void initializeDefaultData() {
        try {
            logger.info("Initializing default system data...");
            
            // Create default roles
            createDefaultRoles();
            
            // Create default admin user
            createDefaultAdminUser();
            
            logger.info("Default system data initialization completed");
            
        } catch (Exception e) {
            logger.error("Failed to initialize default system data", e);
            throw new RuntimeException("Failed to initialize default system data", e);
        }
    }
    
    /**
     * Create default roles with permissions
     */
    private void createDefaultRoles() {
        logger.info("Creating default roles...");
        
        // Create Administrator role
        createRoleIfNotExists(Role.ADMINISTRATOR, "System Administrator", Set.of(
            Role.PERM_SYSTEM_ADMIN,
            Role.PERM_USER_READ, Role.PERM_USER_WRITE, Role.PERM_USER_DELETE,
            Role.PERM_COURSE_READ, Role.PERM_COURSE_WRITE, Role.PERM_COURSE_DELETE,
            Role.PERM_TEST_READ, Role.PERM_TEST_WRITE, Role.PERM_TEST_DELETE, Role.PERM_TEST_GRADE,
            Role.PERM_ATTENDANCE_READ, Role.PERM_ATTENDANCE_WRITE,
            Role.PERM_REPORT_READ, Role.PERM_REPORT_GENERATE
        ));
        
        // Create Teacher role
        createRoleIfNotExists(Role.TEACHER, "Teacher", Set.of(
            Role.PERM_USER_READ,
            Role.PERM_COURSE_READ, Role.PERM_COURSE_WRITE,
            Role.PERM_TEST_READ, Role.PERM_TEST_WRITE, Role.PERM_TEST_GRADE,
            Role.PERM_ATTENDANCE_READ, Role.PERM_ATTENDANCE_WRITE,
            Role.PERM_REPORT_READ
        ));
        
        // Create Student role
        createRoleIfNotExists(Role.STUDENT, "Student", Set.of(
            Role.PERM_COURSE_READ,
            Role.PERM_TEST_READ, Role.PERM_TEST_TAKE,
            Role.PERM_ATTENDANCE_READ
        ));
        
        // Create Staff role
        createRoleIfNotExists(Role.STAFF, "Staff", Set.of(
            Role.PERM_USER_READ,
            Role.PERM_COURSE_READ,
            Role.PERM_ATTENDANCE_READ, Role.PERM_ATTENDANCE_WRITE,
            Role.PERM_REPORT_READ
        ));
        
        logger.info("Default roles created successfully");
    }
    
    /**
     * Create role if it doesn't exist
     */
    private void createRoleIfNotExists(String roleName, String description, Set<String> permissions) {
        try {
            boolean roleExists = dbService.executeReadOnly(session -> {
                String hql = "SELECT COUNT(*) FROM Role WHERE name = :roleName";
                Long count = session.createQuery(hql, Long.class)
                    .setParameter("roleName", roleName)
                    .getSingleResult();
                return count > 0;
            });
            
            if (!roleExists) {
                dbService.executeWithTransaction(session -> {
                    Role role = new Role(roleName, description);
                    role.setPermissions(permissions);
                    session.persist(role);
                });
                
                logger.info("Created role: {} with {} permissions", roleName, permissions.size());
            } else {
                logger.debug("Role already exists: {}", roleName);
            }
            
        } catch (Exception e) {
            logger.error("Failed to create role: {}", roleName, e);
            throw new RuntimeException("Failed to create role: " + roleName, e);
        }
    }
    
    /**
     * Create default admin user
     */
    private void createDefaultAdminUser() {
        try {
            logger.info("Creating default admin user...");
            
            // Check if admin user already exists
            boolean adminExists = dbService.executeReadOnly(session -> {
                String hql = "SELECT COUNT(*) FROM User u JOIN u.role r WHERE r.name = :roleName";
                Long count = session.createQuery(hql, Long.class)
                    .setParameter("roleName", Role.ADMINISTRATOR)
                    .getSingleResult();
                return count > 0;
            });
            
            if (!adminExists) {
                // Create admin user
                AuthService.AuthResult result = authService.register(
                    "admin",
                    "<EMAIL>",
                    "Admin@123",
                    "System",
                    "Administrator",
                    Role.ADMINISTRATOR
                );
                
                if (result.isSuccess()) {
                    logger.info("Default admin user created successfully");
                    logger.info("Admin credentials - Username: admin, Password: Admin@123");
                    logger.warn("IMPORTANT: Please change the default admin password after first login!");
                } else {
                    logger.error("Failed to create default admin user: {}", result.getMessage());
                    throw new RuntimeException("Failed to create default admin user: " + result.getMessage());
                }
            } else {
                logger.debug("Admin user already exists");
            }
            
        } catch (Exception e) {
            logger.error("Failed to create default admin user", e);
            throw new RuntimeException("Failed to create default admin user", e);
        }
    }
    
    /**
     * Check if system is initialized
     */
    public boolean isSystemInitialized() {
        try {
            return dbService.executeReadOnly(session -> {
                // Check if roles exist
                String roleHql = "SELECT COUNT(*) FROM Role";
                Long roleCount = session.createQuery(roleHql, Long.class).getSingleResult();
                
                // Check if admin user exists
                String adminHql = "SELECT COUNT(*) FROM User u JOIN u.role r WHERE r.name = :roleName";
                Long adminCount = session.createQuery(adminHql, Long.class)
                    .setParameter("roleName", Role.ADMINISTRATOR)
                    .getSingleResult();
                
                return roleCount >= 4 && adminCount >= 1; // At least 4 roles and 1 admin
            });
            
        } catch (Exception e) {
            logger.error("Failed to check system initialization status", e);
            return false;
        }
    }
    
    /**
     * Reset system data (for testing purposes)
     */
    public void resetSystemData() {
        logger.warn("Resetting system data - this will delete all data!");
        
        try {
            dbService.executeWithTransaction(session -> {
                // Delete in correct order to avoid foreign key constraints
                session.createQuery("DELETE FROM TestResult").executeUpdate();
                session.createQuery("DELETE FROM Answer").executeUpdate();
                session.createQuery("DELETE FROM Question").executeUpdate();
                session.createQuery("DELETE FROM Test").executeUpdate();
                session.createQuery("DELETE FROM Submission").executeUpdate();
                session.createQuery("DELETE FROM Assignment").executeUpdate();
                session.createQuery("DELETE FROM Grade").executeUpdate();
                session.createQuery("DELETE FROM Attendance").executeUpdate();
                session.createQuery("DELETE FROM Resource").executeUpdate();
                session.createQuery("DELETE FROM Notification").executeUpdate();
                session.createQuery("DELETE FROM SyncRecord").executeUpdate();
                session.createQuery("DELETE FROM User").executeUpdate();
                session.createQuery("DELETE FROM Role").executeUpdate();
            });
            
            logger.info("System data reset completed");
            
        } catch (Exception e) {
            logger.error("Failed to reset system data", e);
            throw new RuntimeException("Failed to reset system data", e);
        }
    }
}
