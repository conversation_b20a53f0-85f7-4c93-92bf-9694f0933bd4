package com.lmssoft.service;

import com.lmssoft.config.DatabaseConfig;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.Consumer;
import java.util.function.Function;

/**
 * Database service for managing database connections and transactions
 * Provides unified access to both offline and online databases
 */
public class DatabaseService {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseService.class);
    private static DatabaseService instance;
    
    private SessionFactory offlineSessionFactory;
    private SessionFactory onlineSessionFactory;
    private boolean isOnlineMode = false;
    
    private DatabaseService() {}
    
    public static DatabaseService getInstance() {
        if (instance == null) {
            synchronized (DatabaseService.class) {
                if (instance == null) {
                    instance = new DatabaseService();
                }
            }
        }
        return instance;
    }
    
    /**
     * Initialize database service
     */
    public void initialize() {
        try {
            logger.info("Initializing database service...");
            
            // Initialize offline database (SQLite)
            offlineSessionFactory = DatabaseConfig.getOfflineSessionFactory();
            logger.info("Offline database initialized successfully");
            
            // Try to initialize online database (MySQL)
            try {
                if (DatabaseConfig.testOnlineConnection()) {
                    onlineSessionFactory = DatabaseConfig.getOnlineSessionFactory();
                    isOnlineMode = true;
                    logger.info("Online database initialized successfully");
                } else {
                    logger.warn("Online database not available, using offline mode");
                }
            } catch (Exception e) {
                logger.warn("Failed to connect to online database, using offline mode: {}", e.getMessage());
            }
            
            logger.info("Database service initialized in {} mode", isOnlineMode ? "online" : "offline");
            
        } catch (Exception e) {
            logger.error("Failed to initialize database service", e);
            throw new RuntimeException("Failed to initialize database service", e);
        }
    }
    
    /**
     * Get current session factory based on mode
     */
    public SessionFactory getCurrentSessionFactory() {
        return isOnlineMode && onlineSessionFactory != null ? 
               onlineSessionFactory : offlineSessionFactory;
    }
    
    /**
     * Get offline session factory
     */
    public SessionFactory getOfflineSessionFactory() {
        return offlineSessionFactory;
    }
    
    /**
     * Get online session factory
     */
    public SessionFactory getOnlineSessionFactory() {
        return onlineSessionFactory;
    }
    
    /**
     * Check if in online mode
     */
    public boolean isOnlineMode() {
        return isOnlineMode;
    }
    
    /**
     * Switch to online mode
     */
    public void switchToOnlineMode() {
        if (onlineSessionFactory != null) {
            isOnlineMode = true;
            logger.info("Switched to online mode");
        } else {
            logger.warn("Cannot switch to online mode - online database not available");
        }
    }
    
    /**
     * Switch to offline mode
     */
    public void switchToOfflineMode() {
        isOnlineMode = false;
        logger.info("Switched to offline mode");
    }
    
    /**
     * Execute operation with transaction
     */
    public <T> T executeWithTransaction(Function<Session, T> operation) {
        SessionFactory sessionFactory = getCurrentSessionFactory();
        Transaction transaction = null;
        
        try (Session session = sessionFactory.openSession()) {
            transaction = session.beginTransaction();
            T result = operation.apply(session);
            transaction.commit();
            return result;
            
        } catch (Exception e) {
            if (transaction != null) {
                try {
                    transaction.rollback();
                } catch (Exception rollbackEx) {
                    logger.error("Failed to rollback transaction", rollbackEx);
                }
            }
            logger.error("Database operation failed", e);
            throw new RuntimeException("Database operation failed", e);
        }
    }
    
    /**
     * Execute operation with transaction (no return value)
     */
    public void executeWithTransaction(Consumer<Session> operation) {
        executeWithTransaction(session -> {
            operation.accept(session);
            return null;
        });
    }
    
    /**
     * Execute read-only operation
     */
    public <T> T executeReadOnly(Function<Session, T> operation) {
        SessionFactory sessionFactory = getCurrentSessionFactory();
        
        try (Session session = sessionFactory.openSession()) {
            session.setDefaultReadOnly(true);
            return operation.apply(session);
            
        } catch (Exception e) {
            logger.error("Read-only database operation failed", e);
            throw new RuntimeException("Read-only database operation failed", e);
        }
    }
    
    /**
     * Test database connectivity
     */
    public boolean testConnection() {
        try {
            SessionFactory sessionFactory = getCurrentSessionFactory();
            try (Session session = sessionFactory.openSession()) {
                session.createNativeQuery("SELECT 1").getSingleResult();
                return true;
            }
        } catch (Exception e) {
            logger.error("Database connection test failed", e);
            return false;
        }
    }
    
    /**
     * Test online database connectivity
     */
    public boolean testOnlineConnection() {
        return DatabaseConfig.testOnlineConnection();
    }
    
    /**
     * Test offline database connectivity
     */
    public boolean testOfflineConnection() {
        return DatabaseConfig.testOfflineConnection();
    }
    
    /**
     * Get database statistics
     */
    public DatabaseStats getDatabaseStats() {
        return executeReadOnly(session -> {
            DatabaseStats stats = new DatabaseStats();
            
            try {
                // Get table counts
                stats.userCount = ((Number) session.createQuery("SELECT COUNT(*) FROM User").getSingleResult()).longValue();
                stats.courseCount = ((Number) session.createQuery("SELECT COUNT(*) FROM Course").getSingleResult()).longValue();
                stats.testCount = ((Number) session.createQuery("SELECT COUNT(*) FROM Test").getSingleResult()).longValue();
                stats.questionCount = ((Number) session.createQuery("SELECT COUNT(*) FROM Question").getSingleResult()).longValue();
                stats.testResultCount = ((Number) session.createQuery("SELECT COUNT(*) FROM TestResult").getSingleResult()).longValue();
                stats.assignmentCount = ((Number) session.createQuery("SELECT COUNT(*) FROM Assignment").getSingleResult()).longValue();
                stats.attendanceCount = ((Number) session.createQuery("SELECT COUNT(*) FROM Attendance").getSingleResult()).longValue();
                
                stats.isOnlineMode = isOnlineMode;
                
            } catch (Exception e) {
                logger.warn("Failed to get some database statistics", e);
            }
            
            return stats;
        });
    }
    
    /**
     * Shutdown database service
     */
    public void shutdown() {
        try {
            logger.info("Shutting down database service...");
            DatabaseConfig.shutdown();
            logger.info("Database service shutdown completed");
            
        } catch (Exception e) {
            logger.error("Error during database service shutdown", e);
        }
    }
    
    /**
     * Database statistics class
     */
    public static class DatabaseStats {
        public long userCount;
        public long courseCount;
        public long testCount;
        public long questionCount;
        public long testResultCount;
        public long assignmentCount;
        public long attendanceCount;
        public boolean isOnlineMode;
        
        @Override
        public String toString() {
            return "DatabaseStats{" +
                    "userCount=" + userCount +
                    ", courseCount=" + courseCount +
                    ", testCount=" + testCount +
                    ", questionCount=" + questionCount +
                    ", testResultCount=" + testResultCount +
                    ", assignmentCount=" + assignmentCount +
                    ", attendanceCount=" + attendanceCount +
                    ", isOnlineMode=" + isOnlineMode +
                    '}';
        }
    }
}
