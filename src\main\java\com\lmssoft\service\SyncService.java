package com.lmssoft.service;

import com.lmssoft.config.AppConfig;
import com.lmssoft.model.SyncRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Synchronization service for offline-online data sync
 * Manages data synchronization between local and remote databases
 */
public class SyncService {
    
    private static final Logger logger = LoggerFactory.getLogger(SyncService.class);
    private static SyncService instance;
    
    private final DatabaseService dbService;
    private final AppConfig config;
    private final ScheduledExecutorService scheduler;
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicBoolean isSyncing = new AtomicBoolean(false);
    
    private SyncService() {
        this.dbService = DatabaseService.getInstance();
        this.config = AppConfig.getInstance();
        this.scheduler = Executors.newScheduledThreadPool(2);
    }
    
    public static SyncService getInstance() {
        if (instance == null) {
            synchronized (SyncService.class) {
                if (instance == null) {
                    instance = new SyncService();
                }
            }
        }
        return instance;
    }
    
    /**
     * Start sync service
     */
    public void start() {
        if (isRunning.compareAndSet(false, true)) {
            logger.info("Starting sync service...");
            
            // Schedule periodic sync
            if (config.isAutoSyncEnabled()) {
                long intervalMs = config.getSyncInterval();
                scheduler.scheduleAtFixedRate(this::performSync, intervalMs, intervalMs, TimeUnit.MILLISECONDS);
                logger.info("Scheduled automatic sync every {} ms", intervalMs);
            }
            
            logger.info("Sync service started");
        }
    }
    
    /**
     * Stop sync service
     */
    public void stop() {
        if (isRunning.compareAndSet(true, false)) {
            logger.info("Stopping sync service...");
            
            // Wait for current sync to complete
            while (isSyncing.get()) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            
            logger.info("Sync service stopped");
        }
    }
    
    /**
     * Shutdown sync service
     */
    public void shutdown() {
        stop();
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        logger.info("Sync service shutdown completed");
    }
    
    /**
     * Perform manual sync
     */
    public SyncResult performSync() {
        if (!isRunning.get()) {
            return SyncResult.failure("Sync service is not running");
        }
        
        if (!isSyncing.compareAndSet(false, true)) {
            return SyncResult.failure("Sync already in progress");
        }
        
        try {
            logger.debug("Starting sync operation...");
            
            // Check if online database is available
            if (!dbService.testOnlineConnection()) {
                logger.warn("Online database not available, skipping sync");
                return SyncResult.failure("Online database not available");
            }
            
            // Get pending sync records
            List<SyncRecord> pendingRecords = getPendingSyncRecords();
            
            if (pendingRecords.isEmpty()) {
                logger.debug("No pending sync records found");
                return SyncResult.success(0, 0);
            }
            
            logger.info("Found {} pending sync records", pendingRecords.size());
            
            int successCount = 0;
            int failureCount = 0;
            
            // Process each sync record
            for (SyncRecord record : pendingRecords) {
                try {
                    if (processSyncRecord(record)) {
                        successCount++;
                    } else {
                        failureCount++;
                    }
                } catch (Exception e) {
                    logger.error("Failed to process sync record: {}", record, e);
                    failureCount++;
                }
            }
            
            logger.info("Sync completed - Success: {}, Failures: {}", successCount, failureCount);
            
            return SyncResult.success(successCount, failureCount);
            
        } catch (Exception e) {
            logger.error("Sync operation failed", e);
            return SyncResult.failure("Sync operation failed: " + e.getMessage());
            
        } finally {
            isSyncing.set(false);
        }
    }
    
    /**
     * Add entity to sync queue
     */
    public void addToSyncQueue(String entityType, Long entityId, SyncRecord.OperationType operationType) {
        try {
            dbService.executeWithTransaction(session -> {
                // Check if record already exists
                String hql = "FROM SyncRecord WHERE entityType = :entityType AND entityId = :entityId AND syncStatus != :syncedStatus";
                List<SyncRecord> existingRecords = session.createQuery(hql, SyncRecord.class)
                    .setParameter("entityType", entityType)
                    .setParameter("entityId", entityId)
                    .setParameter("syncedStatus", SyncRecord.SyncStatus.SYNCED)
                    .getResultList();
                
                if (!existingRecords.isEmpty()) {
                    // Update existing record
                    SyncRecord existingRecord = existingRecords.get(0);
                    existingRecord.setOperationType(operationType);
                    existingRecord.setSyncStatus(SyncRecord.SyncStatus.PENDING);
                    session.merge(existingRecord);
                } else {
                    // Create new record
                    SyncRecord syncRecord = new SyncRecord(entityType, entityId, operationType);
                    session.persist(syncRecord);
                }
            });
            
            logger.debug("Added to sync queue: {} {} {}", entityType, entityId, operationType);
            
        } catch (Exception e) {
            logger.error("Failed to add entity to sync queue: {} {} {}", entityType, entityId, operationType, e);
        }
    }
    
    /**
     * Get sync status
     */
    public SyncStatus getSyncStatus() {
        try {
            return dbService.executeReadOnly(session -> {
                SyncStatus status = new SyncStatus();
                
                // Count pending records
                String pendingHql = "SELECT COUNT(*) FROM SyncRecord WHERE syncStatus = :pendingStatus";
                status.pendingCount = session.createQuery(pendingHql, Long.class)
                    .setParameter("pendingStatus", SyncRecord.SyncStatus.PENDING)
                    .getSingleResult();
                
                // Count failed records
                String failedHql = "SELECT COUNT(*) FROM SyncRecord WHERE syncStatus = :failedStatus";
                status.failedCount = session.createQuery(failedHql, Long.class)
                    .setParameter("failedStatus", SyncRecord.SyncStatus.FAILED)
                    .getSingleResult();
                
                // Count synced records
                String syncedHql = "SELECT COUNT(*) FROM SyncRecord WHERE syncStatus = :syncedStatus";
                status.syncedCount = session.createQuery(syncedHql, Long.class)
                    .setParameter("syncedStatus", SyncRecord.SyncStatus.SYNCED)
                    .getSingleResult();
                
                status.isOnlineMode = dbService.isOnlineMode();
                status.isSyncing = isSyncing.get();
                status.isRunning = isRunning.get();
                
                return status;
            });
            
        } catch (Exception e) {
            logger.error("Failed to get sync status", e);
            return new SyncStatus();
        }
    }
    
    // Private helper methods
    
    private List<SyncRecord> getPendingSyncRecords() {
        return dbService.executeReadOnly(session -> {
            String hql = "FROM SyncRecord WHERE syncStatus IN (:pendingStatus, :failedStatus) AND syncAttempts < maxRetryAttempts ORDER BY priority DESC, createdAt ASC";
            return session.createQuery(hql, SyncRecord.class)
                .setParameter("pendingStatus", SyncRecord.SyncStatus.PENDING)
                .setParameter("failedStatus", SyncRecord.SyncStatus.FAILED)
                .setMaxResults(100) // Process in batches
                .getResultList();
        });
    }
    
    private boolean processSyncRecord(SyncRecord record) {
        try {
            logger.debug("Processing sync record: {}", record);
            
            // Mark sync attempt
            dbService.executeWithTransaction(session -> {
                SyncRecord managedRecord = session.get(SyncRecord.class, record.getId());
                managedRecord.markSyncAttempt();
                managedRecord.setSyncStatus(SyncRecord.SyncStatus.IN_PROGRESS);
                session.merge(managedRecord);
            });
            
            // TODO: Implement actual sync logic based on entity type and operation
            // This is a placeholder implementation
            boolean success = performEntitySync(record);
            
            // Update sync record status
            dbService.executeWithTransaction(session -> {
                SyncRecord managedRecord = session.get(SyncRecord.class, record.getId());
                if (success) {
                    managedRecord.markSyncSuccess();
                } else {
                    managedRecord.markSyncFailed("Sync operation failed", "Entity sync not implemented");
                }
                session.merge(managedRecord);
            });
            
            return success;
            
        } catch (Exception e) {
            logger.error("Failed to process sync record: {}", record, e);
            
            // Mark as failed
            try {
                dbService.executeWithTransaction(session -> {
                    SyncRecord managedRecord = session.get(SyncRecord.class, record.getId());
                    managedRecord.markSyncFailed(e.getMessage(), e.toString());
                    session.merge(managedRecord);
                });
            } catch (Exception updateEx) {
                logger.error("Failed to update sync record status", updateEx);
            }
            
            return false;
        }
    }
    
    private boolean performEntitySync(SyncRecord record) {
        // TODO: Implement actual entity synchronization logic
        // This would involve:
        // 1. Loading the entity from offline database
        // 2. Converting to appropriate format for online database
        // 3. Performing the operation (CREATE, UPDATE, DELETE) on online database
        // 4. Handling conflicts and errors
        
        logger.debug("Entity sync not yet implemented for: {}", record.getEntityType());
        return false; // Placeholder - return false until implemented
    }
    
    // Helper classes
    
    public static class SyncResult {
        private final boolean success;
        private final String message;
        private final int successCount;
        private final int failureCount;
        
        private SyncResult(boolean success, String message, int successCount, int failureCount) {
            this.success = success;
            this.message = message;
            this.successCount = successCount;
            this.failureCount = failureCount;
        }
        
        public static SyncResult success(int successCount, int failureCount) {
            return new SyncResult(true, "Sync completed", successCount, failureCount);
        }
        
        public static SyncResult failure(String message) {
            return new SyncResult(false, message, 0, 0);
        }
        
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public int getSuccessCount() { return successCount; }
        public int getFailureCount() { return failureCount; }
    }
    
    public static class SyncStatus {
        public long pendingCount;
        public long failedCount;
        public long syncedCount;
        public boolean isOnlineMode;
        public boolean isSyncing;
        public boolean isRunning;
        
        @Override
        public String toString() {
            return "SyncStatus{" +
                    "pendingCount=" + pendingCount +
                    ", failedCount=" + failedCount +
                    ", syncedCount=" + syncedCount +
                    ", isOnlineMode=" + isOnlineMode +
                    ", isSyncing=" + isSyncing +
                    ", isRunning=" + isRunning +
                    '}';
        }
    }
}
