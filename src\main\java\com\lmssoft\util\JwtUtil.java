package com.lmssoft.util;

import com.lmssoft.config.AppConfig;
import com.lmssoft.model.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT utility class for token generation and validation
 * Implements a simple JWT implementation for authentication
 */
public class JwtUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(JwtUtil.class);
    private static final String ALGORITHM = "HS256";
    
    /**
     * Generate JWT token for user
     */
    public static String generateToken(User user) {
        try {
            AppConfig config = AppConfig.getInstance();
            
            // Create header
            Map<String, Object> header = new HashMap<>();
            header.put("alg", ALGORITHM);
            header.put("typ", "JWT");
            
            // Create payload
            Map<String, Object> payload = new HashMap<>();
            payload.put("sub", user.getId().toString());
            payload.put("username", user.getUsername());
            payload.put("email", user.getEmail());
            payload.put("role", user.getRole().getName());
            payload.put("iat", Instant.now().getEpochSecond());
            payload.put("exp", Instant.now().plusMillis(config.getJwtExpiration()).getEpochSecond());
            
            // Encode header and payload
            String encodedHeader = base64UrlEncode(JsonUtil.toJson(header));
            String encodedPayload = base64UrlEncode(JsonUtil.toJson(payload));
            
            // Create signature
            String data = encodedHeader + "." + encodedPayload;
            String signature = createSignature(data, config.getJwtSecret());
            
            return data + "." + signature;
            
        } catch (Exception e) {
            logger.error("Failed to generate JWT token for user: {}", user.getUsername(), e);
            throw new RuntimeException("Failed to generate JWT token", e);
        }
    }
    
    /**
     * Validate JWT token
     */
    public static boolean validateToken(String token) {
        try {
            if (token == null || token.trim().isEmpty()) {
                return false;
            }
            
            String[] parts = token.split("\\.");
            if (parts.length != 3) {
                return false;
            }
            
            String header = parts[0];
            String payload = parts[1];
            String signature = parts[2];
            
            // Verify signature
            AppConfig config = AppConfig.getInstance();
            String data = header + "." + payload;
            String expectedSignature = createSignature(data, config.getJwtSecret());
            
            if (!signature.equals(expectedSignature)) {
                logger.warn("JWT token signature validation failed");
                return false;
            }
            
            // Check expiration
            Map<String, Object> payloadMap = JsonUtil.fromJson(base64UrlDecode(payload), Map.class);
            Long exp = ((Number) payloadMap.get("exp")).longValue();
            
            if (Instant.now().getEpochSecond() > exp) {
                logger.debug("JWT token has expired");
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            logger.warn("JWT token validation failed", e);
            return false;
        }
    }
    
    /**
     * Extract user ID from token
     */
    public static Long getUserIdFromToken(String token) {
        try {
            if (!validateToken(token)) {
                return null;
            }
            
            String[] parts = token.split("\\.");
            String payload = parts[1];
            
            Map<String, Object> payloadMap = JsonUtil.fromJson(base64UrlDecode(payload), Map.class);
            String userIdStr = (String) payloadMap.get("sub");
            
            return Long.parseLong(userIdStr);
            
        } catch (Exception e) {
            logger.warn("Failed to extract user ID from JWT token", e);
            return null;
        }
    }
    
    /**
     * Extract username from token
     */
    public static String getUsernameFromToken(String token) {
        try {
            if (!validateToken(token)) {
                return null;
            }
            
            String[] parts = token.split("\\.");
            String payload = parts[1];
            
            Map<String, Object> payloadMap = JsonUtil.fromJson(base64UrlDecode(payload), Map.class);
            return (String) payloadMap.get("username");
            
        } catch (Exception e) {
            logger.warn("Failed to extract username from JWT token", e);
            return null;
        }
    }
    
    /**
     * Extract role from token
     */
    public static String getRoleFromToken(String token) {
        try {
            if (!validateToken(token)) {
                return null;
            }
            
            String[] parts = token.split("\\.");
            String payload = parts[1];
            
            Map<String, Object> payloadMap = JsonUtil.fromJson(base64UrlDecode(payload), Map.class);
            return (String) payloadMap.get("role");
            
        } catch (Exception e) {
            logger.warn("Failed to extract role from JWT token", e);
            return null;
        }
    }
    
    /**
     * Get token expiration time
     */
    public static LocalDateTime getExpirationFromToken(String token) {
        try {
            if (!validateToken(token)) {
                return null;
            }
            
            String[] parts = token.split("\\.");
            String payload = parts[1];
            
            Map<String, Object> payloadMap = JsonUtil.fromJson(base64UrlDecode(payload), Map.class);
            Long exp = ((Number) payloadMap.get("exp")).longValue();
            
            return LocalDateTime.ofInstant(Instant.ofEpochSecond(exp), ZoneOffset.UTC);
            
        } catch (Exception e) {
            logger.warn("Failed to extract expiration from JWT token", e);
            return null;
        }
    }
    
    /**
     * Check if token is expired
     */
    public static boolean isTokenExpired(String token) {
        LocalDateTime expiration = getExpirationFromToken(token);
        return expiration != null && expiration.isBefore(LocalDateTime.now(ZoneOffset.UTC));
    }
    
    // Private helper methods
    
    private static String createSignature(String data, String secret) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);
            
            byte[] signature = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return base64UrlEncode(signature);
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to create JWT signature", e);
        }
    }
    
    private static String base64UrlEncode(String data) {
        return base64UrlEncode(data.getBytes(StandardCharsets.UTF_8));
    }
    
    private static String base64UrlEncode(byte[] data) {
        return Base64.getUrlEncoder().withoutPadding().encodeToString(data);
    }
    
    private static String base64UrlDecode(String data) {
        byte[] decoded = Base64.getUrlDecoder().decode(data);
        return new String(decoded, StandardCharsets.UTF_8);
    }
}
