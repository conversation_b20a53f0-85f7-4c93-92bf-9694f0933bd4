package com.lmssoft.util;

import com.lmssoft.config.AppConfig;
import javafx.animation.FadeTransition;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;
import javafx.util.Duration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Manages scene transitions and navigation throughout the application
 * Provides smooth animated transitions between different screens
 */
public class SceneManager {
    
    private static final Logger logger = LoggerFactory.getLogger(SceneManager.class);
    private static SceneManager instance;
    
    private Stage primaryStage;
    private Scene currentScene;
    private final Map<String, Parent> sceneCache = new HashMap<>();
    private final AppConfig config;
    
    private SceneManager() {
        this.config = AppConfig.getInstance();
    }
    
    public static SceneManager getInstance() {
        if (instance == null) {
            instance = new SceneManager();
        }
        return instance;
    }
    
    public void setPrimaryStage(Stage primaryStage) {
        this.primaryStage = primaryStage;
    }
    
    public Stage getPrimaryStage() {
        return primaryStage;
    }
    
    /**
     * Show welcome screen with animation
     */
    public void showWelcomeScreen() {
        try {
            Parent root = loadFXML("/fxml/welcome.fxml");
            switchScene(root, "Welcome - " + config.getAppTitle());
        } catch (Exception e) {
            logger.error("Failed to show welcome screen", e);
            throw new RuntimeException("Failed to show welcome screen", e);
        }
    }
    
    /**
     * Show login screen with animation
     */
    public void showLoginScreen() {
        try {
            Parent root = loadFXML("/fxml/login.fxml");
            switchScene(root, "Login - " + config.getAppTitle());
        } catch (Exception e) {
            logger.error("Failed to show login screen", e);
            throw new RuntimeException("Failed to show login screen", e);
        }
    }
    
    /**
     * Show main dashboard based on user role
     */
    public void showDashboard(String userRole) {
        try {
            String fxmlPath = getDashboardFXMLPath(userRole);
            Parent root = loadFXML(fxmlPath);
            switchScene(root, "Dashboard - " + config.getAppTitle());
        } catch (Exception e) {
            logger.error("Failed to show dashboard for role: {}", userRole, e);
            throw new RuntimeException("Failed to show dashboard", e);
        }
    }
    
    /**
     * Show CBT test interface
     */
    public void showTestInterface(Long testId) {
        try {
            Parent root = loadFXML("/fxml/cbt/test-interface.fxml");
            switchScene(root, "Test - " + config.getAppTitle());
        } catch (Exception e) {
            logger.error("Failed to show test interface for test ID: {}", testId, e);
            throw new RuntimeException("Failed to show test interface", e);
        }
    }
    
    /**
     * Show course management interface
     */
    public void showCourseManagement() {
        try {
            Parent root = loadFXML("/fxml/lms/course-management.fxml");
            switchScene(root, "Course Management - " + config.getAppTitle());
        } catch (Exception e) {
            logger.error("Failed to show course management", e);
            throw new RuntimeException("Failed to show course management", e);
        }
    }
    
    /**
     * Show attendance management interface
     */
    public void showAttendanceManagement() {
        try {
            Parent root = loadFXML("/fxml/attendance/attendance-management.fxml");
            switchScene(root, "Attendance Management - " + config.getAppTitle());
        } catch (Exception e) {
            logger.error("Failed to show attendance management", e);
            throw new RuntimeException("Failed to show attendance management", e);
        }
    }
    
    /**
     * Show user profile interface
     */
    public void showUserProfile() {
        try {
            Parent root = loadFXML("/fxml/user/profile.fxml");
            switchScene(root, "Profile - " + config.getAppTitle());
        } catch (Exception e) {
            logger.error("Failed to show user profile", e);
            throw new RuntimeException("Failed to show user profile", e);
        }
    }
    
    /**
     * Show settings interface
     */
    public void showSettings() {
        try {
            Parent root = loadFXML("/fxml/settings/settings.fxml");
            switchScene(root, "Settings - " + config.getAppTitle());
        } catch (Exception e) {
            logger.error("Failed to show settings", e);
            throw new RuntimeException("Failed to show settings", e);
        }
    }
    
    /**
     * Load FXML file and return root node
     */
    private Parent loadFXML(String fxmlPath) throws IOException {
        // Check cache first
        if (sceneCache.containsKey(fxmlPath)) {
            return sceneCache.get(fxmlPath);
        }
        
        FXMLLoader loader = new FXMLLoader(getClass().getResource(fxmlPath));
        Parent root = loader.load();
        
        // Cache the loaded scene
        sceneCache.put(fxmlPath, root);
        
        logger.debug("Loaded FXML: {}", fxmlPath);
        return root;
    }
    
    /**
     * Switch to new scene with fade animation
     */
    private void switchScene(Parent newRoot, String title) {
        if (primaryStage == null) {
            throw new IllegalStateException("Primary stage not set");
        }
        
        if (currentScene == null) {
            // First scene - no animation
            currentScene = new Scene(newRoot);
            primaryStage.setScene(currentScene);
            primaryStage.setTitle(title);
            primaryStage.show();
        } else {
            // Animate transition
            animateSceneTransition(newRoot, title);
        }
    }
    
    /**
     * Animate scene transition with fade effect
     */
    private void animateSceneTransition(Parent newRoot, String title) {
        Parent currentRoot = currentScene.getRoot();
        
        // Fade out current scene
        FadeTransition fadeOut = new FadeTransition(
            Duration.millis(config.getAnimationDuration() / 2), currentRoot);
        fadeOut.setFromValue(1.0);
        fadeOut.setToValue(0.0);
        
        fadeOut.setOnFinished(event -> {
            // Switch to new scene
            currentScene = new Scene(newRoot);
            primaryStage.setScene(currentScene);
            primaryStage.setTitle(title);
            
            // Fade in new scene
            newRoot.setOpacity(0.0);
            FadeTransition fadeIn = new FadeTransition(
                Duration.millis(config.getAnimationDuration() / 2), newRoot);
            fadeIn.setFromValue(0.0);
            fadeIn.setToValue(1.0);
            fadeIn.play();
        });
        
        fadeOut.play();
    }
    
    /**
     * Get dashboard FXML path based on user role
     */
    private String getDashboardFXMLPath(String userRole) {
        return switch (userRole.toUpperCase()) {
            case "STUDENT" -> "/fxml/dashboard/student-dashboard.fxml";
            case "TEACHER" -> "/fxml/dashboard/teacher-dashboard.fxml";
            case "ADMINISTRATOR" -> "/fxml/dashboard/admin-dashboard.fxml";
            case "STAFF" -> "/fxml/dashboard/staff-dashboard.fxml";
            default -> "/fxml/dashboard/default-dashboard.fxml";
        };
    }
    
    /**
     * Clear scene cache
     */
    public void clearCache() {
        sceneCache.clear();
        logger.debug("Scene cache cleared");
    }
    
    /**
     * Get current scene
     */
    public Scene getCurrentScene() {
        return currentScene;
    }
    
    /**
     * Apply CSS theme to current scene
     */
    public void applyTheme(String themeName) {
        if (currentScene != null) {
            String cssPath = "/css/" + themeName + ".css";
            try {
                currentScene.getStylesheets().clear();
                currentScene.getStylesheets().add(getClass().getResource(cssPath).toExternalForm());
                logger.debug("Applied theme: {}", themeName);
            } catch (Exception e) {
                logger.warn("Failed to apply theme: {}", themeName, e);
            }
        }
    }
}
