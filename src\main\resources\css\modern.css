/* Modern Theme CSS for LMS CBT System */

/* Root Variables */
.root {
    -fx-font-family: "Segoe UI", "Roboto", "Arial", sans-serif;
    -fx-font-size: 14px;
    
    /* Color Palette */
    -primary-color: #2196F3;
    -primary-dark: #1976D2;
    -primary-light: #BBDEFB;
    
    -secondary-color: #FF9800;
    -secondary-dark: #F57C00;
    -secondary-light: #FFE0B2;
    
    -success-color: #4CAF50;
    -warning-color: #FF9800;
    -error-color: #F44336;
    -info-color: #2196F3;
    
    -background-color: #FAFAFA;
    -surface-color: #FFFFFF;
    -card-color: #FFFFFF;
    
    -text-primary: #212121;
    -text-secondary: #757575;
    -text-disabled: #BDBDBD;
    
    -border-color: #E0E0E0;
    -divider-color: #E0E0E0;
    
    /* Shadows */
    -shadow-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    -shadow-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
    -shadow-3: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
    
    /* Border Radius */
    -border-radius-small: 4px;
    -border-radius-medium: 8px;
    -border-radius-large: 12px;
    
    /* Spacing */
    -spacing-xs: 4px;
    -spacing-sm: 8px;
    -spacing-md: 16px;
    -spacing-lg: 24px;
    -spacing-xl: 32px;
}

/* Base Styles */
.root {
    -fx-background-color: -background-color;
}

/* Buttons */
.button {
    -fx-background-color: -primary-color;
    -fx-text-fill: white;
    -fx-font-weight: 500;
    -fx-padding: 12px 24px;
    -fx-background-radius: -border-radius-medium;
    -fx-border-radius: -border-radius-medium;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 4, 0, 0, 2);
}

.button:hover {
    -fx-background-color: -primary-dark;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 6, 0, 0, 3);
}

.button:pressed {
    -fx-background-color: -primary-dark;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.4), 2, 0, 0, 1);
}

.button:disabled {
    -fx-background-color: -text-disabled;
    -fx-text-fill: white;
    -fx-effect: none;
    -fx-cursor: default;
}

/* Secondary Button */
.button.secondary {
    -fx-background-color: transparent;
    -fx-text-fill: -primary-color;
    -fx-border-color: -primary-color;
    -fx-border-width: 2px;
    -fx-effect: none;
}

.button.secondary:hover {
    -fx-background-color: -primary-light;
    -fx-text-fill: -primary-dark;
}

/* Success Button */
.button.success {
    -fx-background-color: -success-color;
}

.button.success:hover {
    -fx-background-color: derive(-success-color, -20%);
}

/* Warning Button */
.button.warning {
    -fx-background-color: -warning-color;
}

.button.warning:hover {
    -fx-background-color: derive(-warning-color, -20%);
}

/* Error Button */
.button.error {
    -fx-background-color: -error-color;
}

.button.error:hover {
    -fx-background-color: derive(-error-color, -20%);
}

/* Text Fields */
.text-field, .password-field, .text-area {
    -fx-background-color: -surface-color;
    -fx-border-color: -border-color;
    -fx-border-width: 1px;
    -fx-border-radius: -border-radius-medium;
    -fx-background-radius: -border-radius-medium;
    -fx-padding: 12px 16px;
    -fx-font-size: 14px;
    -fx-text-fill: -text-primary;
}

.text-field:focused, .password-field:focused, .text-area:focused {
    -fx-border-color: -primary-color;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, rgba(33, 150, 243, 0.3), 4, 0, 0, 0);
}

.text-field:error, .password-field:error, .text-area:error {
    -fx-border-color: -error-color;
    -fx-border-width: 2px;
}

/* Labels */
.label {
    -fx-text-fill: -text-primary;
    -fx-font-size: 14px;
}

.label.title {
    -fx-font-size: 24px;
    -fx-font-weight: 600;
    -fx-text-fill: -text-primary;
}

.label.subtitle {
    -fx-font-size: 18px;
    -fx-font-weight: 500;
    -fx-text-fill: -text-secondary;
}

.label.caption {
    -fx-font-size: 12px;
    -fx-text-fill: -text-secondary;
}

.label.error {
    -fx-text-fill: -error-color;
    -fx-font-size: 12px;
}

/* Cards */
.card {
    -fx-background-color: -card-color;
    -fx-background-radius: -border-radius-large;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 8, 0, 0, 2);
    -fx-padding: -spacing-lg;
}

.card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 12, 0, 0, 4);
}

/* Navigation */
.navigation-bar {
    -fx-background-color: -surface-color;
    -fx-border-color: -divider-color;
    -fx-border-width: 0 0 1px 0;
    -fx-padding: -spacing-md;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 1);
}

.nav-button {
    -fx-background-color: transparent;
    -fx-text-fill: -text-secondary;
    -fx-padding: 8px 16px;
    -fx-background-radius: -border-radius-medium;
    -fx-cursor: hand;
}

.nav-button:hover {
    -fx-background-color: -primary-light;
    -fx-text-fill: -primary-dark;
}

.nav-button.active {
    -fx-background-color: -primary-color;
    -fx-text-fill: white;
}

/* Sidebar */
.sidebar {
    -fx-background-color: -surface-color;
    -fx-border-color: -divider-color;
    -fx-border-width: 0 1px 0 0;
    -fx-padding: -spacing-md;
    -fx-min-width: 250px;
}

.sidebar-item {
    -fx-background-color: transparent;
    -fx-text-fill: -text-primary;
    -fx-padding: 12px 16px;
    -fx-background-radius: -border-radius-medium;
    -fx-cursor: hand;
    -fx-alignment: center-left;
}

.sidebar-item:hover {
    -fx-background-color: -primary-light;
}

.sidebar-item.active {
    -fx-background-color: -primary-color;
    -fx-text-fill: white;
}

/* Tables */
.table-view {
    -fx-background-color: -surface-color;
    -fx-border-color: -border-color;
    -fx-border-width: 1px;
    -fx-border-radius: -border-radius-medium;
    -fx-background-radius: -border-radius-medium;
}

.table-view .column-header {
    -fx-background-color: -background-color;
    -fx-text-fill: -text-primary;
    -fx-font-weight: 600;
    -fx-padding: 12px 16px;
    -fx-border-color: -border-color;
    -fx-border-width: 0 0 1px 0;
}

.table-view .table-cell {
    -fx-padding: 12px 16px;
    -fx-text-fill: -text-primary;
    -fx-border-color: -border-color;
    -fx-border-width: 0 0 1px 0;
}

.table-view .table-row-cell:selected {
    -fx-background-color: -primary-light;
}

.table-view .table-row-cell:hover {
    -fx-background-color: derive(-primary-light, 50%);
}

/* Progress Indicators */
.progress-bar {
    -fx-background-color: -border-color;
    -fx-background-radius: 10px;
}

.progress-bar .bar {
    -fx-background-color: -primary-color;
    -fx-background-radius: 10px;
}

.progress-indicator {
    -fx-progress-color: -primary-color;
}

/* Dialogs */
.dialog-pane {
    -fx-background-color: -surface-color;
    -fx-background-radius: -border-radius-large;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 20, 0, 0, 10);
}

.dialog-pane .header-panel {
    -fx-background-color: -primary-color;
    -fx-text-fill: white;
    -fx-padding: -spacing-lg;
    -fx-background-radius: -border-radius-large -border-radius-large 0 0;
}

.dialog-pane .content {
    -fx-padding: -spacing-lg;
}

/* Animations */
.fade-in {
    -fx-opacity: 0;
}

.slide-in-left {
    -fx-translate-x: -100;
}

.slide-in-right {
    -fx-translate-x: 100;
}

.slide-in-up {
    -fx-translate-y: 100;
}

.slide-in-down {
    -fx-translate-y: -100;
}

/* Utility Classes */
.text-center {
    -fx-alignment: center;
}

.text-left {
    -fx-alignment: center-left;
}

.text-right {
    -fx-alignment: center-right;
}

.margin-top {
    -fx-padding: -spacing-md 0 0 0;
}

.margin-bottom {
    -fx-padding: 0 0 -spacing-md 0;
}

.margin-left {
    -fx-padding: 0 0 0 -spacing-md;
}

.margin-right {
    -fx-padding: 0 -spacing-md 0 0;
}

.padding {
    -fx-padding: -spacing-md;
}

.padding-large {
    -fx-padding: -spacing-lg;
}

/* Responsive */
.container {
    -fx-max-width: 1200px;
    -fx-alignment: center;
}

.row {
    -fx-spacing: -spacing-md;
}

.col {
    -fx-spacing: -spacing-sm;
}
