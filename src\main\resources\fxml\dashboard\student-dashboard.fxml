<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.lmssoft.controller.StudentDashboardController">
   <!-- Top Navigation Bar -->
   <top>
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="navigation-bar">
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
         
         <Label text="LMS CBT System" styleClass="title">
            <font>
               <Font name="System Bold" size="18.0" />
            </font>
         </Label>
         
         <Region HBox.hgrow="ALWAYS" />
         
         <HBox alignment="CENTER" spacing="15.0">
            <Button fx:id="notificationsButton" onAction="#handleNotifications" styleClass="nav-button" text="🔔 Notifications" />
            <Button fx:id="profileButton" onAction="#handleProfile" styleClass="nav-button" text="👤 Profile" />
            <Button fx:id="logoutButton" onAction="#handleLogout" styleClass="nav-button" text="Logout" />
         </HBox>
      </HBox>
   </top>
   
   <!-- Main Content Area -->
   <center>
      <HBox spacing="0.0">
         <!-- Sidebar Navigation -->
         <VBox styleClass="sidebar" spacing="10.0">
            <padding>
               <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
            </padding>
            
            <Label text="Student Dashboard" styleClass="subtitle">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font>
            </Label>
            
            <Separator />
            
            <VBox spacing="5.0">
               <Button fx:id="dashboardNavButton" onAction="#showDashboard" styleClass="sidebar-item active" text="📊 Dashboard" maxWidth="Infinity" />
               <Button fx:id="coursesNavButton" onAction="#showCourses" styleClass="sidebar-item" text="📚 My Courses" maxWidth="Infinity" />
               <Button fx:id="testsNavButton" onAction="#showTests" styleClass="sidebar-item" text="🖥️ Tests" maxWidth="Infinity" />
               <Button fx:id="assignmentsNavButton" onAction="#showAssignments" styleClass="sidebar-item" text="📝 Assignments" maxWidth="Infinity" />
               <Button fx:id="gradesNavButton" onAction="#showGrades" styleClass="sidebar-item" text="📈 Grades" maxWidth="Infinity" />
               <Button fx:id="attendanceNavButton" onAction="#showAttendance" styleClass="sidebar-item" text="📅 Attendance" maxWidth="Infinity" />
               <Button fx:id="resourcesNavButton" onAction="#showResources" styleClass="sidebar-item" text="📁 Resources" maxWidth="Infinity" />
            </VBox>
         </VBox>
         
         <!-- Main Content Panel -->
         <VBox spacing="20.0" VBox.vgrow="ALWAYS" HBox.hgrow="ALWAYS">
            <padding>
               <Insets bottom="30.0" left="30.0" right="30.0" top="30.0" />
            </padding>
            
            <!-- Welcome Section -->
            <VBox spacing="15.0">
               <Label fx:id="welcomeLabel" text="Welcome back, Student!" styleClass="title">
                  <font>
                     <Font name="System Bold" size="24.0" />
                  </font>
               </Label>
               <Label fx:id="dateLabel" text="Today is Monday, January 15, 2024" styleClass="subtitle">
                  <font>
                     <Font size="14.0" />
                  </font>
               </Label>
            </VBox>
            
            <!-- Quick Stats Cards -->
            <HBox spacing="20.0">
               <VBox spacing="10.0" styleClass="card" HBox.hgrow="ALWAYS">
                  <Label text="Active Courses" styleClass="caption">
                     <font>
                        <Font name="System Bold" size="12.0" />
                     </font>
                  </Label>
                  <Label fx:id="activeCoursesLabel" text="5" styleClass="title">
                     <font>
                        <Font name="System Bold" size="28.0" />
                     </font>
                  </Label>
               </VBox>
               
               <VBox spacing="10.0" styleClass="card" HBox.hgrow="ALWAYS">
                  <Label text="Pending Tests" styleClass="caption">
                     <font>
                        <Font name="System Bold" size="12.0" />
                     </font>
                  </Label>
                  <Label fx:id="pendingTestsLabel" text="3" styleClass="title">
                     <font>
                        <Font name="System Bold" size="28.0" />
                     </font>
                  </Label>
               </VBox>
               
               <VBox spacing="10.0" styleClass="card" HBox.hgrow="ALWAYS">
                  <Label text="Assignments Due" styleClass="caption">
                     <font>
                        <Font name="System Bold" size="12.0" />
                     </font>
                  </Label>
                  <Label fx:id="assignmentsDueLabel" text="2" styleClass="title">
                     <font>
                        <Font name="System Bold" size="28.0" />
                     </font>
                  </Label>
               </VBox>
               
               <VBox spacing="10.0" styleClass="card" HBox.hgrow="ALWAYS">
                  <Label text="Overall Grade" styleClass="caption">
                     <font>
                        <Font name="System Bold" size="12.0" />
                     </font>
                  </Label>
                  <Label fx:id="overallGradeLabel" text="85%" styleClass="title">
                     <font>
                        <Font name="System Bold" size="28.0" />
                     </font>
                  </Label>
               </VBox>
            </HBox>
            
            <!-- Recent Activity and Upcoming Events -->
            <HBox spacing="20.0" VBox.vgrow="ALWAYS">
               <!-- Recent Activity -->
               <VBox spacing="15.0" styleClass="card" HBox.hgrow="ALWAYS">
                  <Label text="Recent Activity" styleClass="subtitle">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  
                  <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
                     <VBox fx:id="recentActivityContainer" spacing="10.0">
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <Label text="📝" />
                           <VBox spacing="2.0" HBox.hgrow="ALWAYS">
                              <Label text="Submitted Math Assignment #3" styleClass="text-primary">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label text="2 hours ago" styleClass="caption" />
                           </VBox>
                        </HBox>
                        
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <Label text="🖥️" />
                           <VBox spacing="2.0" HBox.hgrow="ALWAYS">
                              <Label text="Completed Science Quiz" styleClass="text-primary">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label text="Yesterday" styleClass="caption" />
                           </VBox>
                        </HBox>
                        
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <Label text="📚" />
                           <VBox spacing="2.0" HBox.hgrow="ALWAYS">
                              <Label text="Accessed English Course Materials" styleClass="text-primary">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label text="2 days ago" styleClass="caption" />
                           </VBox>
                        </HBox>
                     </VBox>
                  </ScrollPane>
               </VBox>
               
               <!-- Upcoming Events -->
               <VBox spacing="15.0" styleClass="card" HBox.hgrow="ALWAYS">
                  <Label text="Upcoming Events" styleClass="subtitle">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  
                  <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
                     <VBox fx:id="upcomingEventsContainer" spacing="10.0">
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <Label text="🖥️" />
                           <VBox spacing="2.0" HBox.hgrow="ALWAYS">
                              <Label text="Physics Test" styleClass="text-primary">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label text="Tomorrow at 10:00 AM" styleClass="caption" />
                           </VBox>
                        </HBox>
                        
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <Label text="📝" />
                           <VBox spacing="2.0" HBox.hgrow="ALWAYS">
                              <Label text="History Assignment Due" styleClass="text-primary">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label text="Friday at 11:59 PM" styleClass="caption" />
                           </VBox>
                        </HBox>
                        
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <Label text="📅" />
                           <VBox spacing="2.0" HBox.hgrow="ALWAYS">
                              <Label text="Chemistry Lab Session" styleClass="text-primary">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label text="Next Monday at 2:00 PM" styleClass="caption" />
                           </VBox>
                        </HBox>
                     </VBox>
                  </ScrollPane>
               </VBox>
            </HBox>
            
            <!-- Quick Actions -->
            <HBox spacing="15.0" alignment="CENTER">
               <Button fx:id="takeTestButton" onAction="#handleTakeTest" text="Take Available Test" styleClass="primary">
                  <font>
                     <Font name="System Bold" size="14.0" />
                  </font>
               </Button>
               <Button fx:id="viewCoursesButton" onAction="#handleViewCourses" text="View My Courses" styleClass="secondary">
                  <font>
                     <Font name="System Bold" size="14.0" />
                  </font>
               </Button>
               <Button fx:id="checkGradesButton" onAction="#handleCheckGrades" text="Check Grades" styleClass="secondary">
                  <font>
                     <Font name="System Bold" size="14.0" />
                  </font>
               </Button>
            </HBox>
         </VBox>
      </HBox>
   </center>
   
   <!-- Status Bar -->
   <bottom>
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="navigation-bar">
         <padding>
            <Insets bottom="10.0" left="20.0" right="20.0" top="10.0" />
         </padding>
         <Label fx:id="statusLabel" text="Ready" styleClass="caption" />
         <Region HBox.hgrow="ALWAYS" />
         <Label fx:id="connectionStatusLabel" text="Online" styleClass="caption" />
      </HBox>
   </bottom>
</BorderPane>
