<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.lmssoft.controller.TeacherDashboardController">
   <center>
      <VBox alignment="CENTER" spacing="40.0" styleClass="container">
         <padding>
            <Insets bottom="50.0" left="50.0" right="50.0" top="50.0" />
         </padding>
         
         <Label text="Teacher Dashboard" styleClass="title">
            <font>
               <Font name="System Bold" size="36.0" />
            </font>
         </Label>
         
         <Label text="Welcome to the Teacher Dashboard! This interface will be implemented in a future version." styleClass="subtitle" textAlignment="CENTER" wrapText="true">
            <font>
               <Font size="18.0" />
            </font>
         </Label>
         
         <HBox alignment="CENTER" spacing="20.0">
            <Button fx:id="backButton" onAction="#handleBack" text="Back to Login" styleClass="secondary">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font>
            </Button>
            <Button fx:id="logoutButton" onAction="#handleLogout" text="Logout" styleClass="primary">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font>
            </Button>
         </HBox>
      </VBox>
   </center>
</BorderPane>
