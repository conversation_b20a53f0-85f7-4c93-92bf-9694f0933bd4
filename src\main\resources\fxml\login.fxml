<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.lmssoft.controller.LoginController">
   <center>
      <HBox alignment="CENTER" spacing="50.0">
         <padding>
            <Insets bottom="50.0" left="50.0" right="50.0" top="50.0" />
         </padding>
         
         <!-- Left Side - Welcome Message -->
         <VBox alignment="CENTER" spacing="30.0" maxWidth="400.0">
            <Label styleClass="title" text="Welcome Back!" textAlignment="CENTER">
               <font>
                  <Font name="System Bold" size="32.0" />
               </font>
            </Label>
            <Label styleClass="subtitle" text="Sign in to access your learning dashboard" textAlignment="CENTER" wrapText="true">
               <font>
                  <Font size="16.0" />
               </font>
            </Label>
            
            <!-- Feature Highlights -->
            <VBox spacing="15.0">
               <HBox alignment="CENTER_LEFT" spacing="10.0">
                  <Label text="✓" styleClass="success">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  <Label text="Access your courses and assignments" wrapText="true">
                     <font>
                        <Font size="14.0" />
                     </font>
                  </Label>
               </HBox>
               <HBox alignment="CENTER_LEFT" spacing="10.0">
                  <Label text="✓" styleClass="success">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  <Label text="Take tests and view results" wrapText="true">
                     <font>
                        <Font size="14.0" />
                     </font>
                  </Label>
               </HBox>
               <HBox alignment="CENTER_LEFT" spacing="10.0">
                  <Label text="✓" styleClass="success">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  <Label text="Track your progress and attendance" wrapText="true">
                     <font>
                        <Font size="14.0" />
                     </font>
                  </Label>
               </HBox>
            </VBox>
         </VBox>
         
         <!-- Right Side - Login Form -->
         <VBox alignment="CENTER" spacing="25.0" styleClass="card" maxWidth="400.0" minWidth="350.0">
            <padding>
               <Insets bottom="40.0" left="40.0" right="40.0" top="40.0" />
            </padding>
            
            <!-- Form Header -->
            <VBox alignment="CENTER" spacing="10.0">
               <Label text="Sign In" styleClass="title">
                  <font>
                     <Font name="System Bold" size="24.0" />
                  </font>
               </Label>
               <Label text="Enter your credentials to continue" styleClass="caption">
                  <font>
                     <Font size="14.0" />
                  </font>
               </Label>
            </VBox>
            
            <!-- Login Form -->
            <VBox spacing="20.0">
               <!-- Username/Email Field -->
               <VBox spacing="5.0">
                  <Label text="Username or Email">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <TextField fx:id="usernameField" promptText="Enter your username or email" />
                  <Label fx:id="usernameErrorLabel" styleClass="error" visible="false" />
               </VBox>
               
               <!-- Password Field -->
               <VBox spacing="5.0">
                  <Label text="Password">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <PasswordField fx:id="passwordField" promptText="Enter your password" />
                  <Label fx:id="passwordErrorLabel" styleClass="error" visible="false" />
               </VBox>
               
               <!-- Remember Me and Forgot Password -->
               <HBox alignment="CENTER_LEFT" spacing="10.0">
                  <CheckBox fx:id="rememberMeCheckBox" text="Remember me">
                     <font>
                        <Font size="14.0" />
                     </font>
                  </CheckBox>
                  <Region HBox.hgrow="ALWAYS" />
                  <Hyperlink fx:id="forgotPasswordLink" onAction="#handleForgotPassword" text="Forgot Password?">
                     <font>
                        <Font size="12.0" />
                     </font>
                  </Hyperlink>
               </HBox>
               
               <!-- Error Message -->
               <Label fx:id="errorMessageLabel" styleClass="error" visible="false" wrapText="true" />
               
               <!-- Login Button -->
               <Button fx:id="loginButton" defaultButton="true" maxWidth="Infinity" onAction="#handleLogin" prefHeight="45.0" text="Sign In">
                  <font>
                     <Font name="System Bold" size="16.0" />
                  </font>
               </Button>
               
               <!-- Loading Indicator -->
               <HBox alignment="CENTER">
                  <ProgressIndicator fx:id="loadingIndicator" maxHeight="30.0" maxWidth="30.0" visible="false" />
               </HBox>
               
               <!-- Divider -->
               <Separator />
               
               <!-- Register Link -->
               <HBox alignment="CENTER" spacing="5.0">
                  <Label text="Don't have an account?">
                     <font>
                        <Font size="14.0" />
                     </font>
                  </Label>
                  <Hyperlink fx:id="registerLink" onAction="#handleRegister" text="Sign Up">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Hyperlink>
               </HBox>
            </VBox>
         </VBox>
      </HBox>
   </center>
   
   <!-- Top Navigation -->
   <top>
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="navigation-bar">
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
         <Button fx:id="backButton" onAction="#handleBack" styleClass="nav-button" text="← Back to Welcome" />
         <Region HBox.hgrow="ALWAYS" />
         <Label fx:id="systemStatusLabel" styleClass="caption" text="System Status: Online" />
      </HBox>
   </top>
   
   <!-- Bottom Footer -->
   <bottom>
      <HBox alignment="CENTER" spacing="20.0" styleClass="navigation-bar">
         <padding>
            <Insets bottom="10.0" left="20.0" right="20.0" top="10.0" />
         </padding>
         <Label text="© 2024 LMS CBT System" styleClass="caption">
            <font>
               <Font size="12.0" />
            </font>
         </Label>
         <Region HBox.hgrow="ALWAYS" />
         <Button fx:id="helpButton" onAction="#handleHelp" styleClass="nav-button" text="Help" />
      </HBox>
   </bottom>
</BorderPane>
