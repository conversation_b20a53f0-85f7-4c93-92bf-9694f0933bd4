<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.lmssoft.controller.WelcomeController">
   <center>
      <VBox alignment="CENTER" spacing="40.0" styleClass="container">
         <padding>
            <Insets bottom="50.0" left="50.0" right="50.0" top="50.0" />
         </padding>
         
         <!-- Welcome Header -->
         <VBox alignment="CENTER" spacing="20.0">
            <Label styleClass="title" text="Welcome to LMS CBT System" textAlignment="CENTER">
               <font>
                  <Font name="System Bold" size="36.0" />
               </font>
            </Label>
            <Label styleClass="subtitle" text="Learning Management System with Computer-Based Testing" textAlignment="CENTER">
               <font>
                  <Font size="18.0" />
               </font>
            </Label>
         </VBox>
         
         <!-- Feature Cards -->
         <HBox alignment="CENTER" spacing="30.0">
            <VBox alignment="CENTER" spacing="15.0" styleClass="card" maxWidth="250.0" minHeight="200.0">
               <Label styleClass="subtitle" text="📚 Course Management" textAlignment="CENTER">
                  <font>
                     <Font name="System Bold" size="16.0" />
                  </font>
               </Label>
               <Label text="Create and manage courses, assignments, and educational resources with ease." textAlignment="CENTER" wrapText="true">
                  <font>
                     <Font size="14.0" />
                  </font>
               </Label>
            </VBox>
            
            <VBox alignment="CENTER" spacing="15.0" styleClass="card" maxWidth="250.0" minHeight="200.0">
               <Label styleClass="subtitle" text="🖥️ Computer-Based Testing" textAlignment="CENTER">
                  <font>
                     <Font name="System Bold" size="16.0" />
                  </font>
               </Label>
               <Label text="Advanced CBT system with multiple question types, auto-grading, and real-time monitoring." textAlignment="CENTER" wrapText="true">
                  <font>
                     <Font size="14.0" />
                  </font>
               </Label>
            </VBox>
            
            <VBox alignment="CENTER" spacing="15.0" styleClass="card" maxWidth="250.0" minHeight="200.0">
               <Label styleClass="subtitle" text="📊 Analytics & Reports" textAlignment="CENTER">
                  <font>
                     <Font name="System Bold" size="16.0" />
                  </font>
               </Label>
               <Label text="Comprehensive analytics, attendance tracking, and detailed performance reports." textAlignment="CENTER" wrapText="true">
                  <font>
                     <Font size="14.0" />
                  </font>
               </Label>
            </VBox>
         </HBox>
         
         <!-- Action Buttons -->
         <VBox alignment="CENTER" spacing="20.0">
            <HBox alignment="CENTER" spacing="20.0">
               <Button fx:id="loginButton" onAction="#handleLogin" prefWidth="150.0" text="Login">
                  <font>
                     <Font name="System Bold" size="16.0" />
                  </font>
               </Button>
               <Button fx:id="registerButton" onAction="#handleRegister" prefWidth="150.0" styleClass="secondary" text="Register">
                  <font>
                     <Font name="System Bold" size="16.0" />
                  </font>
               </Button>
            </HBox>
            
            <Separator maxWidth="300.0" />
            
            <VBox alignment="CENTER" spacing="10.0">
               <Label text="Quick Access" styleClass="caption">
                  <font>
                     <Font name="System Bold" size="12.0" />
                  </font>
               </Label>
               <HBox alignment="CENTER" spacing="15.0">
                  <Button fx:id="guestButton" onAction="#handleGuestAccess" styleClass="secondary" text="Guest Access">
                     <font>
                        <Font size="14.0" />
                     </font>
                  </Button>
                  <Button fx:id="demoButton" onAction="#handleDemo" styleClass="secondary" text="View Demo">
                     <font>
                        <Font size="14.0" />
                     </font>
                  </Button>
               </HBox>
            </VBox>
         </VBox>
         
         <!-- System Status -->
         <VBox alignment="CENTER" spacing="10.0">
            <HBox alignment="CENTER" spacing="20.0">
               <Label fx:id="statusLabel" text="System Status: Online" styleClass="caption">
                  <font>
                     <Font size="12.0" />
                  </font>
               </Label>
               <Label fx:id="versionLabel" text="Version 1.0.0" styleClass="caption">
                  <font>
                     <Font size="12.0" />
                  </font>
               </Label>
            </HBox>
            <ProgressIndicator fx:id="loadingIndicator" maxHeight="20.0" maxWidth="20.0" visible="false" />
         </VBox>
      </VBox>
   </center>
   
   <!-- Footer -->
   <bottom>
      <HBox alignment="CENTER" spacing="20.0" styleClass="navigation-bar">
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
         <Label text="© 2024 LMS CBT System. All rights reserved." styleClass="caption">
            <font>
               <Font size="12.0" />
            </font>
         </Label>
         <Region HBox.hgrow="ALWAYS" />
         <Button fx:id="settingsButton" onAction="#handleSettings" styleClass="nav-button" text="Settings" />
         <Button fx:id="helpButton" onAction="#handleHelp" styleClass="nav-button" text="Help" />
         <Button fx:id="aboutButton" onAction="#handleAbout" styleClass="nav-button" text="About" />
      </HBox>
   </bottom>
</BorderPane>
