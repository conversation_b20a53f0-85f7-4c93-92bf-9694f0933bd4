package com.lmssoft;

import com.lmssoft.config.AppConfig;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Basic test to verify the application setup
 */
public class BasicTest {
    
    @Test
    public void testAppConfigLoading() {
        AppConfig config = AppConfig.getInstance();
        assertNotNull(config);
        assertNotNull(config.getAppTitle());
        assertNotNull(config.getAppVersion());
    }
    
    @Test
    public void testApplicationClassExists() {
        assertDoesNotThrow(() -> {
            Class.forName("com.lmssoft.LMSApplication");
        });
    }
}
