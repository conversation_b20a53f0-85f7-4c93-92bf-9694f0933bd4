@echo off
title LMS CBT System - Learning Management System
cls
echo ========================================
echo LMS CBT System
echo Learning Management System with CBT
echo ========================================
echo.
echo Starting application...
echo.

REM Change to the directory containing this script
cd /d "%~dp0"

REM Run the application with proper JavaFX settings
java -Djava.awt.headless=false -Dfile.encoding=UTF-8 -Dprism.order=sw -jar "lms-cbt-system-1.0.0-jar-with-dependencies.jar"

if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo Application failed to start
    echo ========================================
    echo.
    echo Error code: %ERRORLEVEL%
    echo.
    echo Possible solutions:
    echo 1. Install Java 17 or higher
    echo 2. Make sure JavaFX is available
    echo 3. Check if the JAR file exists
    echo.
    echo Default login credentials:
    echo Username: admin
    echo Password: Admin@123
    echo.
    pause
) else (
    echo.
    echo Application closed normally.
)
