app:
  name: "LMS CBT System"
  version: "1.0.0"
  title: "Learning Management System with CBT"
  
database:
  offline:
    driver: "org.sqlite.JDBC"
    url: "*******************************"
    dialect: "org.hibernate.dialect.SQLiteDialect"
    
  online:
    driver: "com.mysql.cj.jdbc.Driver"
    url: "***********************************"
    username: "lms_user"
    password: "lms_password"
    dialect: "org.hibernate.dialect.MySQL8Dialect"
    
hibernate:
  hbm2ddl.auto: "update"
  show_sql: false
  format_sql: true
  use_sql_comments: true
  connection.pool_size: 10
  
security:
  jwt:
    secret: "lms-cbt-secret-key-2024"
    expiration: 86400000  # 24 hours
  password:
    min_length: 8
    require_special_chars: true
    require_numbers: true
    
ui:
  theme: "modern"
  animation_duration: 300
  window:
    width: 1200
    height: 800
    min_width: 1000
    min_height: 600
    
sync:
  enabled: true
  interval: 300000  # 5 minutes
  retry_attempts: 3
  timeout: 30000    # 30 seconds
  
logging:
  level: "INFO"
  file: "logs/lms-cbt.log"
  max_file_size: "10MB"
  max_files: 5
  
features:
  offline_mode: true
  auto_sync: true
  backup_enabled: true
  multi_language: false
  accessibility: true
