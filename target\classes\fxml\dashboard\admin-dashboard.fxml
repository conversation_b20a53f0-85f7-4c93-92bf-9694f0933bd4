<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.lmssoft.controller.AdminDashboardController">
   <!-- Top Navigation Bar -->
   <top>
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="navigation-bar">
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
         
         <Label text="LMS CBT System - Administrator" styleClass="title">
            <font>
               <Font name="System Bold" size="18.0" />
            </font>
         </Label>
         
         <Region HBox.hgrow="ALWAYS" />
         
         <HBox alignment="CENTER" spacing="15.0">
            <Button fx:id="systemStatusButton" onAction="#handleSystemStatus" styleClass="nav-button" text="⚙️ System Status" />
            <Button fx:id="notificationsButton" onAction="#handleNotifications" styleClass="nav-button" text="🔔 Notifications" />
            <Button fx:id="profileButton" onAction="#handleProfile" styleClass="nav-button" text="👤 Profile" />
            <Button fx:id="logoutButton" onAction="#handleLogout" styleClass="nav-button" text="Logout" />
         </HBox>
      </HBox>
   </top>
   
   <!-- Main Content Area -->
   <center>
      <HBox spacing="0.0">
         <!-- Sidebar Navigation -->
         <VBox styleClass="sidebar" spacing="10.0">
            <padding>
               <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
            </padding>
            
            <Label text="Admin Dashboard" styleClass="subtitle">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font>
            </Label>
            
            <Separator />
            
            <VBox spacing="5.0">
               <Button fx:id="dashboardNavButton" onAction="#showDashboard" styleClass="sidebar-item active" text="📊 Dashboard" maxWidth="Infinity" />
               <Button fx:id="usersNavButton" onAction="#showUsers" styleClass="sidebar-item" text="👥 User Management" maxWidth="Infinity" />
               <Button fx:id="coursesNavButton" onAction="#showCourses" styleClass="sidebar-item" text="📚 Course Management" maxWidth="Infinity" />
               <Button fx:id="testsNavButton" onAction="#showTests" styleClass="sidebar-item" text="🖥️ Test Management" maxWidth="Infinity" />
               <Button fx:id="reportsNavButton" onAction="#showReports" styleClass="sidebar-item" text="📈 Reports & Analytics" maxWidth="Infinity" />
               <Button fx:id="attendanceNavButton" onAction="#showAttendance" styleClass="sidebar-item" text="📅 Attendance" maxWidth="Infinity" />
               <Button fx:id="systemNavButton" onAction="#showSystem" styleClass="sidebar-item" text="⚙️ System Settings" maxWidth="Infinity" />
               <Button fx:id="backupNavButton" onAction="#showBackup" styleClass="sidebar-item" text="💾 Backup & Restore" maxWidth="Infinity" />
            </VBox>
         </VBox>
         
         <!-- Main Content Panel -->
         <VBox spacing="20.0" VBox.vgrow="ALWAYS" HBox.hgrow="ALWAYS">
            <padding>
               <Insets bottom="30.0" left="30.0" right="30.0" top="30.0" />
            </padding>
            
            <!-- Welcome Section -->
            <VBox spacing="15.0">
               <Label fx:id="welcomeLabel" text="Welcome, Administrator!" styleClass="title">
                  <font>
                     <Font name="System Bold" size="24.0" />
                  </font>
               </Label>
               <Label fx:id="dateLabel" text="Today is Monday, January 15, 2024" styleClass="subtitle">
                  <font>
                     <Font size="14.0" />
                  </font>
               </Label>
            </VBox>
            
            <!-- System Overview Cards -->
            <HBox spacing="20.0">
               <VBox spacing="10.0" styleClass="card" HBox.hgrow="ALWAYS">
                  <Label text="Total Users" styleClass="caption">
                     <font>
                        <Font name="System Bold" size="12.0" />
                     </font>
                  </Label>
                  <Label fx:id="totalUsersLabel" text="1,247" styleClass="title">
                     <font>
                        <Font name="System Bold" size="28.0" />
                     </font>
                  </Label>
                  <Label text="↑ 12 new this week" styleClass="success">
                     <font>
                        <Font size="12.0" />
                     </font>
                  </Label>
               </VBox>
               
               <VBox spacing="10.0" styleClass="card" HBox.hgrow="ALWAYS">
                  <Label text="Active Courses" styleClass="caption">
                     <font>
                        <Font name="System Bold" size="12.0" />
                     </font>
                  </Label>
                  <Label fx:id="activeCoursesLabel" text="45" styleClass="title">
                     <font>
                        <Font name="System Bold" size="28.0" />
                     </font>
                  </Label>
                  <Label text="↑ 3 new this month" styleClass="success">
                     <font>
                        <Font size="12.0" />
                     </font>
                  </Label>
               </VBox>
               
               <VBox spacing="10.0" styleClass="card" HBox.hgrow="ALWAYS">
                  <Label text="Tests Conducted" styleClass="caption">
                     <font>
                        <Font name="System Bold" size="12.0" />
                     </font>
                  </Label>
                  <Label fx:id="testsLabel" text="156" styleClass="title">
                     <font>
                        <Font name="System Bold" size="28.0" />
                     </font>
                  </Label>
                  <Label text="8 scheduled today" styleClass="info">
                     <font>
                        <Font size="12.0" />
                     </font>
                  </Label>
               </VBox>
               
               <VBox spacing="10.0" styleClass="card" HBox.hgrow="ALWAYS">
                  <Label text="System Health" styleClass="caption">
                     <font>
                        <Font name="System Bold" size="12.0" />
                     </font>
                  </Label>
                  <Label fx:id="systemHealthLabel" text="Excellent" styleClass="title">
                     <font>
                        <Font name="System Bold" size="28.0" />
                     </font>
                  </Label>
                  <Label fx:id="uptimeLabel" text="99.9% uptime" styleClass="success">
                     <font>
                        <Font size="12.0" />
                     </font>
                  </Label>
               </VBox>
            </HBox>
            
            <!-- Recent Activity and System Alerts -->
            <HBox spacing="20.0" VBox.vgrow="ALWAYS">
               <!-- Recent System Activity -->
               <VBox spacing="15.0" styleClass="card" HBox.hgrow="ALWAYS">
                  <Label text="Recent System Activity" styleClass="subtitle">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  
                  <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
                     <VBox fx:id="recentActivityContainer" spacing="10.0">
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <Label text="👤" />
                           <VBox spacing="2.0" HBox.hgrow="ALWAYS">
                              <Label text="New user registration: <EMAIL>" styleClass="text-primary">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label text="5 minutes ago" styleClass="caption" />
                           </VBox>
                        </HBox>
                        
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <Label text="🖥️" />
                           <VBox spacing="2.0" HBox.hgrow="ALWAYS">
                              <Label text="Test completed: Physics Quiz - Grade 10" styleClass="text-primary">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label text="15 minutes ago" styleClass="caption" />
                           </VBox>
                        </HBox>
                        
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <Label text="📚" />
                           <VBox spacing="2.0" HBox.hgrow="ALWAYS">
                              <Label text="Course created: Advanced Mathematics" styleClass="text-primary">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label text="1 hour ago" styleClass="caption" />
                           </VBox>
                        </HBox>
                        
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <Label text="💾" />
                           <VBox spacing="2.0" HBox.hgrow="ALWAYS">
                              <Label text="System backup completed successfully" styleClass="text-primary">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label text="2 hours ago" styleClass="caption" />
                           </VBox>
                        </HBox>
                     </VBox>
                  </ScrollPane>
               </VBox>
               
               <!-- System Alerts and Notifications -->
               <VBox spacing="15.0" styleClass="card" HBox.hgrow="ALWAYS">
                  <Label text="System Alerts" styleClass="subtitle">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  
                  <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
                     <VBox fx:id="systemAlertsContainer" spacing="10.0">
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <Label text="✅" />
                           <VBox spacing="2.0" HBox.hgrow="ALWAYS">
                              <Label text="All systems operational" styleClass="success">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label text="Database, Authentication, and Sync services running normally" styleClass="caption" />
                           </VBox>
                        </HBox>
                        
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <Label text="⚠️" />
                           <VBox spacing="2.0" HBox.hgrow="ALWAYS">
                              <Label text="Scheduled maintenance reminder" styleClass="warning">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label text="System maintenance scheduled for this weekend" styleClass="caption" />
                           </VBox>
                        </HBox>
                        
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <Label text="📊" />
                           <VBox spacing="2.0" HBox.hgrow="ALWAYS">
                              <Label text="Weekly report ready" styleClass="info">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label text="System usage and performance report available" styleClass="caption" />
                           </VBox>
                        </HBox>
                     </VBox>
                  </ScrollPane>
               </VBox>
            </HBox>
            
            <!-- Quick Actions -->
            <HBox spacing="15.0" alignment="CENTER">
               <Button fx:id="createUserButton" onAction="#handleCreateUser" text="Create New User" styleClass="primary">
                  <font>
                     <Font name="System Bold" size="14.0" />
                  </font>
               </Button>
               <Button fx:id="createCourseButton" onAction="#handleCreateCourse" text="Create Course" styleClass="secondary">
                  <font>
                     <Font name="System Bold" size="14.0" />
                  </font>
               </Button>
               <Button fx:id="viewReportsButton" onAction="#handleViewReports" text="View Reports" styleClass="secondary">
                  <font>
                     <Font name="System Bold" size="14.0" />
                  </font>
               </Button>
               <Button fx:id="systemBackupButton" onAction="#handleSystemBackup" text="Backup System" styleClass="secondary">
                  <font>
                     <Font name="System Bold" size="14.0" />
                  </font>
               </Button>
            </HBox>
         </VBox>
      </HBox>
   </center>
   
   <!-- Status Bar -->
   <bottom>
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="navigation-bar">
         <padding>
            <Insets bottom="10.0" left="20.0" right="20.0" top="10.0" />
         </padding>
         <Label fx:id="statusLabel" text="System Ready" styleClass="caption" />
         <Region HBox.hgrow="ALWAYS" />
         <Label fx:id="connectionStatusLabel" text="Online" styleClass="success" />
         <Label fx:id="syncStatusLabel" text="Sync: Active" styleClass="success" />
         <Label fx:id="usersOnlineLabel" text="Users Online: 247" styleClass="caption" />
      </HBox>
   </bottom>
</BorderPane>
