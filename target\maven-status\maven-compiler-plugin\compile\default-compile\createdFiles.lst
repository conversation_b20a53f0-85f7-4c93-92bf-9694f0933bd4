com\lmssoft\model\SyncRecord$ConflictResolution.class
com\lmssoft\model\Attendance$AttendanceType.class
com\lmssoft\model\Submission.class
com\lmssoft\service\DatabaseService.class
com\lmssoft\service\AuthService$ValidationResult.class
com\lmssoft\util\JwtUtil.class
com\lmssoft\model\Assignment$SubmissionType.class
com\lmssoft\controller\StudentDashboardController.class
com\lmssoft\service\SyncService.class
com\lmssoft\model\Attendance$AttendanceStatus.class
com\lmssoft\service\DataInitializationService.class
com\lmssoft\controller\TeacherDashboardController.class
com\lmssoft\model\Grade$GradeType.class
com\lmssoft\service\AuthService$AuthResult.class
com\lmssoft\model\TestResult$TestResultStatus.class
com\lmssoft\model\Resource$ResourceType.class
com\lmssoft\model\User.class
com\lmssoft\model\Question$QuestionType.class
com\lmssoft\service\SyncService$SyncResult.class
com\lmssoft\model\Attendance.class
com\lmssoft\model\Notification$NotificationType.class
com\lmssoft\config\DatabaseConfig.class
com\lmssoft\controller\WelcomeController.class
com\lmssoft\model\SyncRecord.class
com\lmssoft\model\Course.class
com\lmssoft\model\Notification$Priority.class
com\lmssoft\model\Course$CourseStatus.class
com\lmssoft\model\Notification.class
com\lmssoft\util\SceneManager.class
com\lmssoft\controller\LoginController.class
com\lmssoft\model\SyncRecord$OperationType.class
com\lmssoft\model\Test$TestStatus.class
com\lmssoft\model\TestResult.class
com\lmssoft\model\User$Gender.class
com\lmssoft\model\Resource.class
com\lmssoft\model\Question$DifficultyLevel.class
com\lmssoft\LMSApplication.class
com\lmssoft\model\SyncRecord$SyncStatus.class
com\lmssoft\model\Assignment$AssignmentStatus.class
com\lmssoft\model\Submission$SubmissionStatus.class
com\lmssoft\controller\WelcomeController$1.class
com\lmssoft\controller\LoginController$1.class
com\lmssoft\model\Question.class
com\lmssoft\model\Role.class
com\lmssoft\model\Test$TestType.class
com\lmssoft\service\DatabaseService$DatabaseStats.class
com\lmssoft\model\Assignment.class
com\lmssoft\controller\StaffDashboardController.class
com\lmssoft\service\AuthService.class
com\lmssoft\config\AppConfig.class
com\lmssoft\model\BaseEntity.class
com\lmssoft\controller\AdminDashboardController.class
com\lmssoft\service\SyncService$SyncStatus.class
com\lmssoft\util\JsonUtil.class
com\lmssoft\model\Grade.class
com\lmssoft\model\Test.class
com\lmssoft\model\Answer.class
